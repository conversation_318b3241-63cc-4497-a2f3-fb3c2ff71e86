# CMDB数据合规检查功能需求分析报告

## 1. 现有搜索功能分析

### 1.1 普通搜索功能（ResourceSearch）

**实现位置**：`cmdb-ui/src/modules/cmdb/views/resource_search_2/resourceSearch/`

**核心功能**：
- **搜索方式**：支持两种搜索模式
  - 普通搜索模式：单行关键词搜索，支持通配符
  - 列搜索模式：多行精确搜索，每行一个搜索值
- **筛选条件**：
  - CI类型筛选（支持多选）
  - 高级条件过滤（ConditionFilter组件）
  - 搜索表达式构建
- **结果展示**：卡片形式展示，支持分页
- **数据导出**：Excel格式导出
- **历史管理**：最近搜索记录、收藏功能

**关键API接口**：
```javascript
// 主搜索接口
searchCI({
  q: `_type:(${ciTypeIds.join(';')})${exp ? `,${exp}` : ''}${searchValue ? `,*${searchValue}*` : ''}`,
  count: pageSize,
  page: currentPage,
  sort: '_type'
})

// 偏好设置接口
getPreferenceSearch({ name: '__recent__' })
savePreferenceSearch({ option: {...}, name: '__recent__' })
```

**数据结构**：
```javascript
// 搜索参数
{
  searchValue: '', // 搜索关键词
  selectCITypeIds: [], // 选中的CI类型ID
  expression: '', // 筛选表达式
  currentPage: 1,
  pageSize: 50
}

// 搜索结果
{
  result: [], // CI实例列表
  numfound: 0, // 总数
}
```

### 1.2 关系搜索功能（RelationSearch）

**实现位置**：`cmdb-ui/src/modules/cmdb/views/resource_search_2/relationSearch/`

**核心功能**：
- **搜索配置**：
  - 源CI类型选择（单选）
  - 目标CI类型选择（多选，按层级分组）
  - 关系路径选择（支持多路径）
  - 高级选项：缺失关系搜索（上游/下游）
- **条件过滤**：
  - 源CI类型条件过滤
  - 目标CI类型条件过滤
- **结果展示**：表格形式，支持路径列显示
- **任务保存**：支持保存搜索条件为收藏

**关键API接口**：
```javascript
// 关系路径搜索接口
searchCIRelationPath({
  page: page,
  page_size: pageSize,
  source: { type_id: sourceCIType, q: sourceSearch },
  target: { type_ids: targetCITypes, q: targetExp },
  path: selectedPath,
  missing: missingSearch // 缺失关系搜索模式
})

// 获取关系路径配置
getCITypeRelationPath({
  source_type_id: sourceCIType,
  target_type_ids: targetCITypes.join(',')
})
```

**数据结构**：
```javascript
// 关系搜索参数
{
  sourceCIType: undefined, // 源CI类型ID
  sourceCITypeSearchValue: '', // 源CI搜索关键词
  sourceExpression: '', // 源CI筛选表达式
  targetCITypes: [], // 目标CI类型ID列表
  targetExpression: '', // 目标CI筛选表达式
  selectedPath: [], // 选中的路径
  returnPath: true, // 是否返回路径详情
  missingSearch: '' // 缺失关系搜索模式
}
```

## 2. 技术栈分析

### 2.1 前端技术栈
- **Vue版本**：Vue 2.x
- **状态管理**：Vuex
- **UI组件库**：Ant Design Vue (a-button, a-input, a-select, a-table等)
- **第三方组件**：
  - @riophae/vue-treeselect：树形选择器
  - ExcelJS：Excel文件处理
  - FileSaver：文件下载
  - moment：时间处理

### 2.2 架构模式
- **组件化开发**：每个功能模块都有独立的components目录
- **API层分离**：统一的API调用层（@/modules/cmdb/api/）
- **工具函数复用**：搜索条件保存、偏好设置等公共功能
- **响应式设计**：支持窗口大小自适应

### 2.3 现有页面布局模式
- **搜索前状态**：居中布局，包含标题、搜索框、历史记录
- **搜索后状态**：左右分栏布局（70%-30%），左侧结果列表，右侧详情面板
- **组件复用**：SearchInput、FilterPopover、HistoryList等组件在两种搜索模式间复用

## 3. CMDB数据合规检查功能需求设计

### 3.1 功能概述

数据合规检查功能是基于现有搜索功能的扩展，通过创建定时任务的方式，定期执行普通搜索和关系搜索，找出不符合合规要求的CI实例，并生成报告供用户查看和邮件通知。

### 3.2 核心功能模块

#### 3.2.1 任务管理模块

**页面路径**：`cmdb-ui/src/modules/cmdb/views/compliance_check/`

**功能描述**：
- **任务列表**：以表格形式展示所有合规检查任务
- **任务创建**：基于搜索条件创建新的合规检查任务
- **任务编辑**：修改现有任务的配置
- **任务操作**：启用/禁用、删除、立即执行

**页面布局设计**：
```vue
<template>
  <div class="compliance-check">
    <!-- 页面标题和操作按钮 -->
    <div class="compliance-check-header">
      <h2>数据合规检查</h2>
      <a-button type="primary" @click="createTask">
        <a-icon type="plus" />
        创建任务
      </a-button>
    </div>

    <!-- 任务列表表格 -->
    <a-table
      :columns="taskColumns"
      :dataSource="taskList"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <!-- 任务状态列 -->
      <template slot="status" slot-scope="status">
        <a-badge :status="getStatusBadge(status)" :text="getStatusText(status)" />
      </template>
      
      <!-- 操作列 -->
      <template slot="actions" slot-scope="record">
        <a-button-group size="small">
          <a-button @click="viewTask(record)">查看</a-button>
          <a-button @click="editTask(record)">编辑</a-button>
          <a-button @click="executeTask(record)">执行</a-button>
          <a-dropdown>
            <a-menu slot="overlay">
              <a-menu-item @click="toggleTaskStatus(record)">
                {{ record.enabled ? '禁用' : '启用' }}
              </a-menu-item>
              <a-menu-item @click="deleteTask(record)">删除</a-menu-item>
            </a-menu>
            <a-button>
              更多 <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </a-button-group>
      </template>
    </a-table>
  </div>
</template>
```

**数据结构**：
```javascript
// 任务列表项
{
  id: 1,
  name: '服务器配置合规检查',
  description: '检查服务器CPU和内存配置是否符合标准',
  enabled: true,
  schedule: {
    type: 'weekly', // 'once', 'daily', 'weekly', 'monthly'
    cron: '0 9 * * 1', // cron表达式
    timezone: 'Asia/Shanghai'
  },
  searches: [
    {
      type: 'resource', // 'resource' 或 'relation'
      config: {
        searchValue: '',
        selectCITypeIds: [1, 2],
        expression: 'q=cpu:<4,memory:<8'
      }
    }
  ],
  notification: {
    enabled: true,
    emails: ['<EMAIL>'],
    onlyOnViolation: true // 仅在发现违规时通知
  },
  lastExecuteTime: '2024-01-15 09:00:00',
  nextExecuteTime: '2024-01-22 09:00:00',
  status: 'success', // 'running', 'success', 'failed'
  createTime: '2024-01-01 10:00:00',
  updateTime: '2024-01-15 09:30:00'
}
```

#### 3.2.2 任务创建/编辑模块

**组件路径**：`cmdb-ui/src/modules/cmdb/views/compliance_check/components/TaskForm.vue`

**功能描述**：
- **基本信息配置**：任务名称、描述
- **搜索条件配置**：复用现有的SearchInput和RelationSearch组件
- **执行计划配置**：支持一次性、定时、周期性执行
- **通知配置**：邮件通知设置

**设计要点**：
- 采用步骤式表单设计（a-steps + a-form）
- 复用现有搜索组件，减少开发工作量
- 支持添加多个搜索条件

```vue
<template>
  <a-modal
    :title="isEdit ? '编辑任务' : '创建任务'"
    :visible="visible"
    :width="1200"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-steps :current="currentStep" class="task-form-steps">
      <a-step title="基本信息" />
      <a-step title="搜索条件" />
      <a-step title="执行计划" />
      <a-step title="通知设置" />
    </a-steps>

    <div class="task-form-content">
      <!-- 步骤1: 基本信息 -->
      <div v-show="currentStep === 0" class="step-content">
        <a-form-model :model="form" :rules="rules" ref="basicForm">
          <a-form-model-item label="任务名称" prop="name">
            <a-input v-model="form.name" placeholder="请输入任务名称" />
          </a-form-model-item>
          <a-form-model-item label="任务描述" prop="description">
            <a-textarea v-model="form.description" :rows="3" placeholder="请输入任务描述" />
          </a-form-model-item>
        </a-form-model>
      </div>

      <!-- 步骤2: 搜索条件 -->
      <div v-show="currentStep === 1" class="step-content">
        <div class="search-conditions">
          <div
            v-for="(search, index) in form.searches"
            :key="index"
            class="search-condition-item"
          >
            <div class="search-condition-header">
              <h4>搜索条件 {{ index + 1 }}</h4>
              <a-select v-model="search.type" style="width: 120px">
                <a-select-option value="resource">普通搜索</a-select-option>
                <a-select-option value="relation">关系搜索</a-select-option>
              </a-select>
              <a-button type="danger" size="small" @click="removeSearch(index)">
                删除
              </a-button>
            </div>
            
            <!-- 普通搜索配置 -->
            <ResourceSearchConfig
              v-if="search.type === 'resource'"
              v-model="search.config"
              :CITypeGroup="CITypeGroup"
            />
            
            <!-- 关系搜索配置 -->
            <RelationSearchConfig
              v-else
              v-model="search.config"
              :CITypeGroup="CITypeGroup"
            />
          </div>
          
          <a-button type="dashed" @click="addSearch" style="width: 100%">
            <a-icon type="plus" />
            添加搜索条件
          </a-button>
        </div>
      </div>

      <!-- 步骤3: 执行计划 -->
      <div v-show="currentStep === 2" class="step-content">
        <a-form-model :model="form.schedule" ref="scheduleForm">
          <a-form-model-item label="执行类型">
            <a-radio-group v-model="form.schedule.type">
              <a-radio value="once">立即执行一次</a-radio>
              <a-radio value="daily">每日执行</a-radio>
              <a-radio value="weekly">每周执行</a-radio>
              <a-radio value="monthly">每月执行</a-radio>
            </a-radio-group>
          </a-form-model-item>
          
          <template v-if="form.schedule.type !== 'once'">
            <a-form-model-item label="执行时间">
              <a-time-picker v-model="form.schedule.time" format="HH:mm" />
            </a-form-model-item>
            
            <a-form-model-item v-if="form.schedule.type === 'weekly'" label="执行日期">
              <a-checkbox-group v-model="form.schedule.weekdays">
                <a-checkbox value="1">周一</a-checkbox>
                <a-checkbox value="2">周二</a-checkbox>
                <a-checkbox value="3">周三</a-checkbox>
                <a-checkbox value="4">周四</a-checkbox>
                <a-checkbox value="5">周五</a-checkbox>
                <a-checkbox value="6">周六</a-checkbox>
                <a-checkbox value="0">周日</a-checkbox>
              </a-checkbox-group>
            </a-form-model-item>
            
            <a-form-model-item v-if="form.schedule.type === 'monthly'" label="执行日期">
              <a-select v-model="form.schedule.monthday" style="width: 200px">
                <a-select-option v-for="day in 31" :key="day" :value="day">
                  每月{{ day }}号
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </template>
        </a-form-model>
      </div>

      <!-- 步骤4: 通知设置 -->
      <div v-show="currentStep === 3" class="step-content">
        <a-form-model :model="form.notification" ref="notificationForm">
          <a-form-model-item label="启用通知">
            <a-switch v-model="form.notification.enabled" />
          </a-form-model-item>
          
          <template v-if="form.notification.enabled">
            <a-form-model-item label="通知邮箱">
              <a-select
                v-model="form.notification.emails"
                mode="tags"
                style="width: 100%"
                placeholder="请输入邮箱地址，支持多个"
              />
            </a-form-model-item>
            
            <a-form-model-item label="通知条件">
              <a-radio-group v-model="form.notification.condition">
                <a-radio value="always">每次执行后都通知</a-radio>
                <a-radio value="violation">仅发现违规时通知</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </template>
        </a-form-model>
      </div>
    </div>

    <template slot="footer">
      <a-button v-if="currentStep > 0" @click="prevStep">上一步</a-button>
      <a-button v-if="currentStep < 3" type="primary" @click="nextStep">下一步</a-button>
      <a-button v-if="currentStep === 3" type="primary" @click="handleSubmit">
        {{ isEdit ? '更新' : '创建' }}
      </a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-modal>
</template>
```

#### 3.2.3 任务执行与监控模块

**组件路径**：`cmdb-ui/src/modules/cmdb/views/compliance_check/components/TaskDetail.vue`

**功能描述**：
- **任务详情展示**：任务配置信息查看
- **执行历史**：历史执行记录列表
- **执行状态监控**：实时执行状态显示
- **报告查看**：执行结果报告查看和下载

**页面布局**：
```vue
<template>
  <a-drawer
    title="任务详情"
    :visible="visible"
    :width="1000"
    @close="handleClose"
  >
    <a-tabs>
      <!-- 任务配置标签页 -->
      <a-tab-pane key="config" tab="任务配置">
        <TaskConfigView :task="task" />
      </a-tab-pane>
      
      <!-- 执行历史标签页 -->
      <a-tab-pane key="history" tab="执行历史">
        <a-table
          :columns="historyColumns"
          :dataSource="executionHistory"
          :pagination="historyPagination"
        >
          <template slot="status" slot-scope="status">
            <a-badge :status="getStatusBadge(status)" :text="getStatusText(status)" />
          </template>
          
          <template slot="actions" slot-scope="record">
            <a-button size="small" @click="viewReport(record)">查看报告</a-button>
            <a-button size="small" @click="downloadReport(record)">下载报告</a-button>
          </template>
        </a-table>
      </a-tab-pane>
      
      <!-- 实时监控标签页 -->
      <a-tab-pane key="monitor" tab="实时监控" v-if="task.status === 'running'">
        <TaskMonitor :taskId="task.id" />
      </a-tab-pane>
    </a-tabs>
  </a-drawer>
</template>
```

### 3.3 报告生成模块

#### 3.3.1 报告数据结构

```javascript
// 合规检查报告
{
  reportId: 'R20240115001',
  taskId: 1,
  taskName: '服务器配置合规检查',
  executeTime: '2024-01-15 09:00:00',
  duration: 1500, // 执行耗时（毫秒）
  summary: {
    totalSearches: 2, // 总搜索条件数
    totalViolations: 15, // 总违规项数
    severityBreakdown: {
      high: 3,
      medium: 7,
      low: 5
    }
  },
  results: [
    {
      searchIndex: 1,
      searchType: 'resource', // 'resource' 或 'relation'
      searchConfig: {
        searchValue: '',
        selectCITypeIds: [1],
        expression: 'q=cpu:<4'
      },
      violations: [
        {
          ciId: 'ci_001',
          ciType: '物理服务器',
          ciName: 'server-01',
          violationReason: 'CPU核数不足：当前2核，要求≥4核',
          severity: 'high'
        }
      ],
      violationCount: 8
    }
  ],
  notification: {
    sent: true,
    sentTime: '2024-01-15 09:05:00',
    recipients: ['<EMAIL>']
  }
}
```

#### 3.3.2 报告展示组件

**组件路径**：`cmdb-ui/src/modules/cmdb/views/compliance_check/components/ReportView.vue`

**功能描述**：
- **报告概览**：执行摘要、违规统计
- **违规详情**：按搜索条件分组展示违规项
- **数据可视化**：图表展示违规分布
- **导出功能**：支持PDF、Excel格式导出

```vue
<template>
  <div class="report-view">
    <!-- 报告头部 -->
    <div class="report-header">
      <h3>{{ report.taskName }}</h3>
      <div class="report-meta">
        <span>执行时间：{{ report.executeTime }}</span>
        <span>耗时：{{ formatDuration(report.duration) }}</span>
        <span>报告ID：{{ report.reportId }}</span>
      </div>
    </div>

    <!-- 违规摘要 -->
    <a-row :gutter="16" class="report-summary">
      <a-col :span="6">
        <a-statistic title="总违规项" :value="report.summary.totalViolations" />
      </a-col>
      <a-col :span="6">
        <a-statistic title="高风险" :value="report.summary.severityBreakdown.high" />
      </a-col>
      <a-col :span="6">
        <a-statistic title="中风险" :value="report.summary.severityBreakdown.medium" />
      </a-col>
      <a-col :span="6">
        <a-statistic title="低风险" :value="report.summary.severityBreakdown.low" />
      </a-col>
    </a-row>

    <!-- 违规详情 -->
    <div class="report-details">
      <div
        v-for="(result, index) in report.results"
        :key="index"
        class="search-result-section"
      >
        <h4>搜索条件 {{ index + 1 }}（{{ getSearchTypeText(result.searchType) }}）</h4>
        
        <!-- 搜索配置展示 -->
        <div class="search-config">
          <SearchConfigDisplay :config="result.searchConfig" :type="result.searchType" />
        </div>
        
        <!-- 违规项表格 -->
        <a-table
          :columns="violationColumns"
          :dataSource="result.violations"
          :pagination="false"
          size="small"
        >
          <template slot="severity" slot-scope="severity">
            <a-tag :color="getSeverityColor(severity)">
              {{ getSeverityText(severity) }}
            </a-tag>
          </template>
          
          <template slot="actions" slot-scope="record">
            <a-button size="small" @click="viewCIDetail(record.ciId)">
              查看详情
            </a-button>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="report-actions">
      <a-button type="primary" @click="exportPDF">导出PDF</a-button>
      <a-button @click="exportExcel">导出Excel</a-button>
      <a-button @click="sendEmail">发送邮件</a-button>
    </div>
  </div>
</template>
```

#### 3.3.3 邮件报告模板

邮件报告将包含以下内容：
- **HTML格式报告**：适合在邮件中直接查看
- **PDF附件**：详细报告文件
- **Excel附件**：违规项明细数据

```html
<!-- 邮件HTML模板 -->
<div class="email-report">
  <h2>{{ taskName }} - 合规检查报告</h2>
  
  <div class="report-summary">
    <h3>执行摘要</h3>
    <ul>
      <li>执行时间：{{ executeTime }}</li>
      <li>总违规项：{{ totalViolations }}</li>
      <li>高风险：{{ highRisk }}，中风险：{{ mediumRisk }}，低风险：{{ lowRisk }}</li>
    </ul>
  </div>
  
  <div class="violation-summary">
    <h3>违规分布</h3>
    <table border="1" style="border-collapse: collapse; width: 100%;">
      <tr>
        <th>搜索条件</th>
        <th>违规项数</th>
        <th>主要问题</th>
      </tr>
      <!-- 动态生成违规摘要行 -->
    </table>
  </div>
  
  <p><strong>详细报告请查看附件</strong></p>
  <p>如需查看完整报告，请登录系统：<a href="{{ systemUrl }}">访问合规检查</a></p>
</div>
```

### 3.4 API接口设计

基于现有API架构，新增合规检查相关接口：

```javascript
// 合规检查任务API
// 文件路径：cmdb-ui/src/modules/cmdb/api/complianceCheck.js

import { axios } from '@/utils/request'

// 获取任务列表
export function getComplianceTasks(params) {
  return axios({
    url: '/v0.1/compliance/tasks',
    method: 'GET',
    params
  })
}

// 创建任务
export function createComplianceTask(data) {
  return axios({
    url: '/v0.1/compliance/tasks',
    method: 'POST',
    data
  })
}

// 更新任务
export function updateComplianceTask(taskId, data) {
  return axios({
    url: `/v0.1/compliance/tasks/${taskId}`,
    method: 'PUT',
    data
  })
}

// 删除任务
export function deleteComplianceTask(taskId) {
  return axios({
    url: `/v0.1/compliance/tasks/${taskId}`,
    method: 'DELETE'
  })
}

// 执行任务
export function executeComplianceTask(taskId) {
  return axios({
    url: `/v0.1/compliance/tasks/${taskId}/execute`,
    method: 'POST'
  })
}

// 获取执行历史
export function getTaskExecutionHistory(taskId, params) {
  return axios({
    url: `/v0.1/compliance/tasks/${taskId}/executions`,
    method: 'GET',
    params
  })
}

// 获取执行报告
export function getExecutionReport(executionId) {
  return axios({
    url: `/v0.1/compliance/executions/${executionId}/report`,
    method: 'GET'
  })
}

// 下载报告
export function downloadExecutionReport(executionId, format) {
  return axios({
    url: `/v0.1/compliance/executions/${executionId}/report/download`,
    method: 'GET',
    params: { format }, // 'pdf', 'excel'
    responseType: 'blob'
  })
}

// 发送邮件报告
export function sendEmailReport(executionId, emails) {
  return axios({
    url: `/v0.1/compliance/executions/${executionId}/email`,
    method: 'POST',
    data: { emails }
  })
}
```

### 3.5 目录结构设计

```
cmdb-ui/src/modules/cmdb/views/compliance_check/
├── index.vue                          # 主页面
├── components/
│   ├── TaskForm.vue                   # 任务创建/编辑表单
│   ├── TaskDetail.vue                 # 任务详情抽屉
│   ├── TaskConfigView.vue             # 任务配置展示
│   ├── TaskMonitor.vue                # 实时监控组件
│   ├── ReportView.vue                 # 报告查看组件
│   ├── SearchConfigDisplay.vue        # 搜索配置展示组件
│   ├── ResourceSearchConfig.vue       # 普通搜索配置组件
│   └── RelationSearchConfig.vue       # 关系搜索配置组件
├── api/
│   └── complianceCheck.js             # 合规检查API
└── utils/
    ├── reportGenerator.js             # 报告生成工具
    ├── emailTemplate.js               # 邮件模板
    └── scheduleUtils.js               # 调度工具函数
```

## 4. 技术实现要点

### 4.1 组件复用策略

- **搜索组件复用**：直接复用现有的SearchInput、FilterPopover等组件
- **数据展示复用**：复用现有的AttrDisplay、CIIcon等展示组件
- **工具函数复用**：复用现有的导出、分页等工具函数

### 4.2 状态管理

```javascript
// Vuex store模块
const complianceCheckStore = {
  namespaced: true,
  state: {
    taskList: [],
    currentTask: null,
    executionHistory: [],
    currentReport: null
  },
  mutations: {
    SET_TASK_LIST(state, tasks) {
      state.taskList = tasks
    },
    SET_CURRENT_TASK(state, task) {
      state.currentTask = task
    },
    SET_EXECUTION_HISTORY(state, history) {
      state.executionHistory = history
    },
    SET_CURRENT_REPORT(state, report) {
      state.currentReport = report
    }
  },
  actions: {
    async fetchTaskList({ commit }, params) {
      const res = await getComplianceTasks(params)
      commit('SET_TASK_LIST', res.tasks)
      return res
    },
    async executeTask({ dispatch }, taskId) {
      await executeComplianceTask(taskId)
      // 刷新任务列表
      dispatch('fetchTaskList')
    }
  }
}
```

### 4.3 后端调度支持评估

**Celery调度能力评估**：
- ✅ **定时任务支持**：Celery支持cron表达式定时任务
- ✅ **任务队列管理**：支持任务排队、重试、失败处理
- ✅ **分布式执行**：支持多worker并行执行
- ✅ **任务监控**：提供任务状态监控接口
- ✅ **持久化存储**：支持Redis/数据库作为broker和result backend

**建议的后端实现**：
```python
# 合规检查任务调度
from celery import Celery
from celery.schedules import crontab

@app.task
def execute_compliance_task(task_id):
    """执行合规检查任务"""
    task = ComplianceTask.objects.get(id=task_id)
    
    # 执行搜索
    violations = []
    for search_config in task.searches:
        if search_config['type'] == 'resource':
            results = execute_resource_search(search_config['config'])
        else:
            results = execute_relation_search(search_config['config'])
        violations.extend(results)
    
    # 生成报告
    report = generate_report(task, violations)
    
    # 发送通知
    if task.notification['enabled'] and should_notify(report):
        send_notification(task, report)
    
    return report

# 动态任务调度
def update_task_schedule(task):
    """更新任务调度"""
    revoke_task(f"compliance_task_{task.id}")  # 撤销旧任务
    
    if task.enabled and task.schedule['type'] != 'once':
        # 添加新的定时任务
        app.conf.beat_schedule[f"compliance_task_{task.id}"] = {
            'task': 'execute_compliance_task',
            'schedule': crontab(**parse_schedule(task.schedule)),
            'args': (task.id,)
        }
```

## 5. 实施计划

### 5.1 开发阶段划分

**第一阶段**（2周）：
- 创建基础页面结构
- 实现任务管理模块
- 复用现有搜索组件创建任务配置功能

**第二阶段**（2周）：
- 实现任务执行与监控模块
- 开发报告生成和展示功能
- 完成后端API开发

**第三阶段**（1周）：
- 实现邮件通知功能
- 完成导出功能
- 集成测试和优化

### 5.2 技术风险评估

**低风险**：
- 前端组件复用度高，开发工作量可控
- 现有搜索API稳定，无需大幅修改

**中风险**：
- 后端调度功能需要新开发
- 报告生成性能需要优化

**风险缓解**：
- 采用渐进式开发，先实现核心功能
- 提前进行性能测试和优化

## 6. 总结

基于对现有resource_search_2代码的深入分析，数据合规检查功能可以很好地复用现有的搜索基础设施，通过添加任务管理、调度执行、报告生成等模块，构建一个完整的合规检查解决方案。该方案充分利用了现有技术栈的优势，最大化代码复用，最小化开发风险。
