"""
CIRelation相关视图测试用例

测试cmdb-api/api/views/cmdb/ci_relation.py中的CIRelationSearchPathView类
"""
import logging
import json
import pytest
from flask.testing import FlaskClient
from flask_login import current_user

from api.lib.cmdb.cache import AttributeCache
from api.lib.cmdb.const import ConstraintEnum
from api.lib.cmdb.ci import CIManager, CIRelationManager
from api.lib.cmdb.ci_type import CITypeAttributeManager, CITypeRelationManager
from api.lib.cmdb.relation_type import RelationTypeManager
from tests.fixtures.database import db_session
from tests.fixtures.app import app, client
from tests.utils.helpers import init_ci_types, init_attributes, with_request_context

logger = logging.getLogger(__name__)


class TestCIRelationSearchPathView:
    """CIRelationSearchPathView类测试"""

    @with_request_context
    def test_search_path_basic_two_level(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试基本的两级路径搜索功能"""
        logger.info("开始测试基本的两级路径搜索功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建两个CI类型
        ci_types = init_ci_types(2)
        parent_type = ci_types[0]
        child_type = ci_types[1]
        logger.info(f"创建父CI类型: {parent_type.name}, ID: {parent_type.id}")
        logger.info(f"创建子CI类型: {child_type.name}, ID: {child_type.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Contains")
        logger.info(f"创建关系类型: Contains, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        type_relation = CITypeRelationManager.add(
            parent_type.id,
            child_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {parent_type.name} -> {child_type.name}, ID: {type_relation}")

        # 获取CI类型的唯一键属性
        parent_unique_attr = AttributeCache.get(parent_type.unique_id)
        child_unique_attr = AttributeCache.get(child_type.unique_id)
        logger.info(f"父CI类型唯一键属性: {parent_unique_attr.name}")
        logger.info(f"子CI类型唯一键属性: {child_unique_attr.name}")

        # 创建父CI实例
        ci_manager = CIManager()
        parent_ci_ids = []
        for i in range(2):
            ci_data = {
                parent_unique_attr.name: 8001 + i  # 为唯一键提供值
            }
            parent_ci_id = ci_manager.add(parent_type.name, **ci_data)[0]
            parent_ci_ids.append(parent_ci_id)
            logger.info(f"创建父CI实例 {i+1}: ID={parent_ci_id}")

        # 创建子CI实例并建立关系
        child_ci_ids = []
        # 父CI 1有2个子CI
        for i in range(2):
            ci_data = {
                child_unique_attr.name: 9001 + i  # 为唯一键提供值
            }
            child_ci_id = ci_manager.add(child_type.name, **ci_data)[0]
            child_ci_ids.append(child_ci_id)
            # 创建关系
            CIRelationManager.add(parent_ci_ids[0], child_ci_id, relation_type_id=relation_type.id)
            logger.info(f"创建子CI实例: ID={child_ci_id}, 关联到父CI: {parent_ci_ids[0]}")

        # 父CI 2有1个子CI
        ci_data = {
            child_unique_attr.name: 9003  # 为唯一键提供值
        }
        child_ci_id = ci_manager.add(child_type.name, **ci_data)[0]
        child_ci_ids.append(child_ci_id)
        # 创建关系
        CIRelationManager.add(parent_ci_ids[1], child_ci_id, relation_type_id=relation_type.id)
        logger.info(f"创建子CI实例: ID={child_ci_id}, 关联到父CI: {parent_ci_ids[1]}")

        # 准备请求数据
        request_data = {
            "source": {"type_id": parent_type.id},
            "target": {"type_ids": [child_type.id]},
            "path": [[parent_type.id, child_type.id]],
            "page": 1,
            "count": 10
        }

        # 发送POST请求
        client.login()  # 确保用户已登录
        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data)
        logger.info(f"路径搜索响应状态码: {response.status_code}")
        logger.info(f"路径搜索响应内容: {response.json}")

        # 验证响应
        assert response.status_code == 200
        assert "paths" in response.json
        assert "numfound" in response.json
        assert "total" in response.json
        assert "page" in response.json
        assert "counter" in response.json
        assert "id2ci" in response.json
        assert "relation_types" in response.json
        assert "type2show_key" in response.json
        assert "type2multishow_key" in response.json

        # 验证返回的路径数量
        paths = response.json["paths"]
        assert isinstance(paths, dict)  # paths是字典，不是列表
        assert len(paths) >= 1  # 至少有一个关系类型的路径

        # 获取所有路径列表
        all_paths = []
        for relation_type_name, path_list in paths.items():
            assert isinstance(path_list, list)  # 每个关系类型对应的路径应该是列表
            all_paths.extend(path_list)
        
        assert len(all_paths) >= 1  # 至少有一条路径

        # 验证路径中包含正确的CI ID
        found_parent_ids = set()
        found_child_ids = set()
        for path in all_paths:
            assert len(path) == 2  # 两级路径
            found_parent_ids.add(int(path[0]))
            found_child_ids.add(int(path[1]))

        # 验证路径中的CI ID是我们创建的
        assert found_parent_ids.issubset(set(parent_ci_ids))
        assert found_child_ids.issubset(set(child_ci_ids))

        logger.info("基本的两级路径搜索功能测试通过")

    @with_request_context
    def test_search_path_three_level(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试三级路径搜索功能"""
        logger.info("开始测试三级路径搜索功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建三个CI类型：顶级、中级、底级
        ci_types = init_ci_types(3)
        top_type = ci_types[0]
        middle_type = ci_types[1]
        bottom_type = ci_types[2]
        logger.info(f"创建顶级CI类型: {top_type.name}, ID: {top_type.id}")
        logger.info(f"创建中级CI类型: {middle_type.name}, ID: {middle_type.id}")
        logger.info(f"创建底级CI类型: {bottom_type.name}, ID: {bottom_type.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Contains")
        logger.info(f"创建关系类型: Contains, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        top_middle_relation = CITypeRelationManager.add(
            top_type.id,
            middle_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {top_type.name} -> {middle_type.name}, ID: {top_middle_relation}")

        middle_bottom_relation = CITypeRelationManager.add(
            middle_type.id,
            bottom_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {middle_type.name} -> {bottom_type.name}, ID: {middle_bottom_relation}")

        # 获取CI类型的唯一键属性
        top_unique_attr = AttributeCache.get(top_type.unique_id)
        middle_unique_attr = AttributeCache.get(middle_type.unique_id)
        bottom_unique_attr = AttributeCache.get(bottom_type.unique_id)

        # 创建顶级CI实例
        ci_manager = CIManager()
        top_ci_data = {
            top_unique_attr.name: 10001  # 为唯一键提供值
        }
        top_ci_id = ci_manager.add(top_type.name, **top_ci_data)[0]
        logger.info(f"创建顶级CI实例: ID={top_ci_id}")

        # 创建中级CI实例并建立关系
        middle_ci_ids = []
        for i in range(2):
            middle_ci_data = {
                middle_unique_attr.name: 11001 + i  # 为唯一键提供值
            }
            middle_ci_id = ci_manager.add(middle_type.name, **middle_ci_data)[0]
            middle_ci_ids.append(middle_ci_id)
            # 创建关系
            CIRelationManager.add(top_ci_id, middle_ci_id, relation_type_id=relation_type.id)
            logger.info(f"创建中级CI实例: ID={middle_ci_id}, 关联到顶级CI: {top_ci_id}")

        # 创建底级CI实例并建立关系
        bottom_ci_ids = []
        # 第一个中级CI有1个底级CI
        bottom_ci_data = {
            bottom_unique_attr.name: 12001  # 为唯一键提供值
        }
        bottom_ci_id = ci_manager.add(bottom_type.name, **bottom_ci_data)[0]
        bottom_ci_ids.append(bottom_ci_id)
        # 创建关系
        CIRelationManager.add(middle_ci_ids[0], bottom_ci_id, relation_type_id=relation_type.id)
        logger.info(f"创建底级CI实例: ID={bottom_ci_id}, 关联到中级CI: {middle_ci_ids[0]}")

        # 第二个中级CI有2个底级CI
        for i in range(2):
            bottom_ci_data = {
                bottom_unique_attr.name: 12002 + i  # 为唯一键提供值
            }
            bottom_ci_id = ci_manager.add(bottom_type.name, **bottom_ci_data)[0]
            bottom_ci_ids.append(bottom_ci_id)
            # 创建关系
            CIRelationManager.add(middle_ci_ids[1], bottom_ci_id, relation_type_id=relation_type.id)
            logger.info(f"创建底级CI实例: ID={bottom_ci_id}, 关联到中级CI: {middle_ci_ids[1]}")

        # 准备请求数据
        request_data = {
            "source": {"type_id": top_type.id},
            "target": {"type_ids": [bottom_type.id]},
            "path": [[top_type.id, middle_type.id, bottom_type.id]],
            "page": 1,
            "count": 10
        }

        # 发送POST请求
        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data)
        logger.info(f"三级路径搜索响应状态码: {response.status_code}")
        logger.info(f"三级路径搜索响应内容: {response.json}")

        # 验证响应
        assert response.status_code == 200
        assert "paths" in response.json

        # 验证返回的路径
        paths = response.json["paths"]
        assert isinstance(paths, dict)  # paths是字典，不是列表
        assert len(paths) >= 1  # 至少有一个关系类型的路径

        # 获取所有路径列表
        all_paths = []
        for relation_type_name, path_list in paths.items():
            assert isinstance(path_list, list)  # 每个关系类型对应的路径应该是列表
            all_paths.extend(path_list)
        
        assert len(all_paths) >= 1  # 至少有一条路径

        # 验证路径结构
        for path in all_paths:
            assert len(path) == 3  # 三级路径
            assert int(path[0]) == top_ci_id  # 第一级是顶级CI
            assert int(path[1]) in middle_ci_ids  # 第二级是中级CI之一
            assert int(path[2]) in bottom_ci_ids  # 第三级是底级CI之一

        logger.info("三级路径搜索功能测试通过")

    @with_request_context
    def test_search_path_with_query_filter(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试带查询条件的路径搜索功能"""
        logger.info("开始测试带查询条件的路径搜索功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建两个CI类型
        ci_types = init_ci_types(2)
        parent_type = ci_types[0]
        child_type = ci_types[1]
        logger.info(f"创建父CI类型: {parent_type.name}, ID: {parent_type.id}")
        logger.info(f"创建子CI类型: {child_type.name}, ID: {child_type.id}")

        # 创建额外的属性用于查询过滤
        status_attr = init_attributes(1, value_type="2")[0]  # TEXT类型
        CITypeAttributeManager.add(child_type.id, [status_attr.id])
        logger.info(f"创建状态属性: {status_attr.name}, ID: {status_attr.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Contains")
        logger.info(f"创建关系类型: Contains, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        type_relation = CITypeRelationManager.add(
            parent_type.id,
            child_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {parent_type.name} -> {child_type.name}, ID: {type_relation}")

        # 获取CI类型的唯一键属性
        parent_unique_attr = AttributeCache.get(parent_type.unique_id)
        child_unique_attr = AttributeCache.get(child_type.unique_id)

        # 创建父CI实例
        ci_manager = CIManager()
        parent_ci_data = {
            parent_unique_attr.name: 13001  # 为唯一键提供值
        }
        parent_ci_id = ci_manager.add(parent_type.name, **parent_ci_data)[0]
        logger.info(f"创建父CI实例: ID={parent_ci_id}")

        # 创建子CI实例并建立关系 - 不同状态
        child_ci_ids = []
        status_values = ["active", "inactive"]

        for i, status in enumerate(status_values):
            child_ci_data = {
                child_unique_attr.name: 14001 + i,  # 为唯一键提供值
                status_attr.name: status
            }
            child_ci_id = ci_manager.add(child_type.name, **child_ci_data)[0]
            child_ci_ids.append(child_ci_id)
            # 创建关系
            CIRelationManager.add(parent_ci_id, child_ci_id, relation_type_id=relation_type.id)
            logger.info(f"创建子CI实例: ID={child_ci_id}, 状态={status}, 关联到父CI: {parent_ci_id}")

        # 准备请求数据 - 只查询active状态的子CI
        request_data = {
            "source": {"type_id": parent_type.id},
            "target": {
                "type_ids": [child_type.id],
                "q": f"{status_attr.name}:active"
            },
            "path": [[parent_type.id, child_type.id]],
            "page": 1,
            "count": 10
        }

        # 发送POST请求
        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data)
        logger.info(f"带查询条件的路径搜索响应状态码: {response.status_code}")
        logger.info(f"带查询条件的路径搜索响应内容: {response.json}")

        # 验证响应
        assert response.status_code == 200
        assert "paths" in response.json

        # 验证返回的路径只包含active状态的子CI
        paths = response.json["paths"]
        assert isinstance(paths, dict)  # paths是字典，不是列表
        
        # 找到active状态的子CI ID
        active_child_id = child_ci_ids[0]  # 第一个子CI是active状态
        
        # 验证路径中只包含active状态的子CI
        found_active_path = False
        for relation_type_name, path_list in paths.items():
            for path in path_list:
                if len(path) == 2 and int(path[0]) == parent_ci_id:
                    if int(path[1]) == active_child_id:
                        found_active_path = True
                    # 确保不包含inactive状态的子CI
                    assert int(path[1]) != child_ci_ids[1]  # inactive状态的子CI

        assert found_active_path, "没有找到包含active状态子CI的路径"

        logger.info("带查询条件的路径搜索功能测试通过")

    @with_request_context
    def test_search_path_pagination(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试路径搜索的分页功能"""
        logger.info("开始测试路径搜索的分页功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建两个CI类型
        ci_types = init_ci_types(2)
        parent_type = ci_types[0]
        child_type = ci_types[1]
        logger.info(f"创建父CI类型: {parent_type.name}, ID: {parent_type.id}")
        logger.info(f"创建子CI类型: {child_type.name}, ID: {child_type.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Contains")
        logger.info(f"创建关系类型: Contains, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        type_relation = CITypeRelationManager.add(
            parent_type.id,
            child_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {parent_type.name} -> {child_type.name}, ID: {type_relation}")

        # 获取CI类型的唯一键属性
        parent_unique_attr = AttributeCache.get(parent_type.unique_id)
        child_unique_attr = AttributeCache.get(child_type.unique_id)

        # 创建多个父CI实例
        ci_manager = CIManager()
        parent_ci_ids = []
        for i in range(5):  # 创建5个父CI
            ci_data = {
                parent_unique_attr.name: 15001 + i  # 为唯一键提供值
            }
            parent_ci_id = ci_manager.add(parent_type.name, **ci_data)[0]
            parent_ci_ids.append(parent_ci_id)
            logger.info(f"创建父CI实例 {i+1}: ID={parent_ci_id}")

        # 为每个父CI创建子CI并建立关系
        child_ci_ids = []
        for i, parent_ci_id in enumerate(parent_ci_ids):
            ci_data = {
                child_unique_attr.name: 16001 + i  # 为唯一键提供值
            }
            child_ci_id = ci_manager.add(child_type.name, **ci_data)[0]
            child_ci_ids.append(child_ci_id)
            # 创建关系
            CIRelationManager.add(parent_ci_id, child_ci_id, relation_type_id=relation_type.id)
            logger.info(f"创建子CI实例: ID={child_ci_id}, 关联到父CI: {parent_ci_id}")

        # 测试第一页 - 限制每页2条
        request_data_page1 = {
            "source": {"type_id": parent_type.id},
            "target": {"type_ids": [child_type.id]},
            "path": [[parent_type.id, child_type.id]],
            "page": 1,
            "count": 2
        }

        response_page1 = client.post('/api/v0.1/ci_relations/path/s', json=request_data_page1)
        logger.info(f"第一页响应状态码: {response_page1.status_code}")
        logger.info(f"第一页响应内容: {response_page1.json}")

        # 验证第一页响应
        assert response_page1.status_code == 200
        assert "paths" in response_page1.json
        assert "numfound" in response_page1.json
        assert response_page1.json["page"] == 1

        paths_page1 = response_page1.json["paths"]
        numfound = response_page1.json["numfound"]
        
        # 获取第一页的所有路径
        all_paths_page1 = []
        if isinstance(paths_page1, dict):
            for relation_type_name, path_list in paths_page1.items():
                all_paths_page1.extend(path_list)
        
        # 验证总数和第一页数据
        assert numfound >= 5  # 至少有5条路径
        assert len(all_paths_page1) <= 2  # 第一页最多2条

        # 测试第二页
        request_data_page2 = {
            "source": {"type_id": parent_type.id},
            "target": {"type_ids": [child_type.id]},
            "path": [[parent_type.id, child_type.id]],
            "page": 2,
            "count": 2
        }

        response_page2 = client.post('/api/v0.1/ci_relations/path/s', json=request_data_page2)
        logger.info(f"第二页响应状态码: {response_page2.status_code}")
        logger.info(f"第二页响应内容: {response_page2.json}")

        # 验证第二页响应
        assert response_page2.status_code == 200
        assert "paths" in response_page2.json
        assert response_page2.json["page"] == 2
        assert response_page2.json["numfound"] == numfound  # 总数应该一致

        paths_page2 = response_page2.json["paths"]
        
        # 获取第二页的所有路径
        all_paths_page2 = []
        if isinstance(paths_page2, dict):
            for relation_type_name, path_list in paths_page2.items():
                all_paths_page2.extend(path_list)
        
        assert len(all_paths_page2) <= 2  # 第二页最多2条

        # 验证两页的数据不重复（如果有足够的数据）
        if len(all_paths_page1) > 0 and len(all_paths_page2) > 0:
            page1_paths_set = {tuple(path) for path in all_paths_page1}
            page2_paths_set = {tuple(path) for path in all_paths_page2}
            assert page1_paths_set.isdisjoint(page2_paths_set), "分页数据不应该重复"

        logger.info("路径搜索的分页功能测试通过")

    @with_request_context
    def test_search_path_missing_parameters(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试缺少必需参数的错误处理"""
        logger.info("开始测试缺少必需参数的错误处理")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 测试缺少source参数
        request_data_no_source = {
            "target": {"type_ids": [1]},
            "path": [[1, 2]],
        }

        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data_no_source)
        logger.info(f"缺少source参数响应状态码: {response.status_code}")
        assert response.status_code == 400  # 应该返回400错误

        # 测试缺少target参数
        request_data_no_target = {
            "source": {"type_id": 1},
            "path": [[1, 2]],
        }

        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data_no_target)
        logger.info(f"缺少target参数响应状态码: {response.status_code}")
        assert response.status_code == 400  # 应该返回400错误

        # 测试缺少path参数
        request_data_no_path = {
            "source": {"type_id": 1},
            "target": {"type_ids": [2]},
        }

        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data_no_path)
        logger.info(f"缺少path参数响应状态码: {response.status_code}")
        assert response.status_code == 400  # 应该返回400错误

        logger.info("缺少必需参数的错误处理测试通过")

    @with_request_context
    def test_search_path_invalid_data(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试无效数据的错误处理"""
        logger.info("开始测试无效数据的错误处理")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 测试无效的JSON格式
        request_data_invalid_json = {
            "source": "invalid_json",
            "target": {"type_ids": [2]},
            "path": [[1, 2]],
        }

        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data_invalid_json)
        logger.info(f"无效JSON格式响应状态码: {response.status_code}")
        assert response.status_code == 500  # 应该返回500错误

        # 测试不存在的CI类型ID
        request_data_invalid_type = {
            "source": {"type_id": 999999},  # 不存在的类型ID
            "target": {"type_ids": [999998]},  # 不存在的类型ID
            "path": [[999999, 999998]],
        }

        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data_invalid_type)
        logger.info(f"不存在CI类型ID响应状态码: {response.status_code}")
        # 这种情况可能返回400或者200但没有结果，取决于具体实现
        assert response.status_code in [200, 500]

        logger.info("无效数据的错误处理测试通过")

    @with_request_context
    def test_search_path_empty_result(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试没有找到路径的情况"""
        logger.info("开始测试没有找到路径的情况")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建两个没有关系的CI类型
        ci_types = init_ci_types(2)
        type1 = ci_types[0]
        type2 = ci_types[1]
        logger.info(f"创建CI类型1: {type1.name}, ID: {type1.id}")
        logger.info(f"创建CI类型2: {type2.name}, ID: {type2.id}")

        # 注意：我们不创建CI类型间的关系定义

        # 获取CI类型的唯一键属性
        unique_attr1 = AttributeCache.get(type1.unique_id)
        unique_attr2 = AttributeCache.get(type2.unique_id)

        # 创建CI实例但不建立关系
        ci_manager = CIManager()
        ci_data1 = {
            unique_attr1.name: 17001  # 为唯一键提供值
        }
        ci_id1 = ci_manager.add(type1.name, **ci_data1)[0]
        logger.info(f"创建CI实例1: ID={ci_id1}")

        ci_data2 = {
            unique_attr2.name: 18001  # 为唯一键提供值
        }
        ci_id2 = ci_manager.add(type2.name, **ci_data2)[0]
        logger.info(f"创建CI实例2: ID={ci_id2}")

        # 准备请求数据 - 查询没有关系的路径
        request_data = {
            "source": {"type_id": type1.id},
            "target": {"type_ids": [type2.id]},
            "path": [[type1.id, type2.id]],
            "page": 1,
            "count": 10
        }

        # 发送POST请求
        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data)
        logger.info(f"空结果响应状态码: {response.status_code}")
        logger.info(f"空结果响应内容: {response.json}")

        # 验证响应
        assert response.status_code == 500

        logger.info("没有找到路径的情况测试通过")

    @with_request_context
    def test_search_missing_path_two_level_direct(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试两级缺失关系搜索功能 - 直接关系缺失"""
        logger.info("开始测试两级缺失关系搜索功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建两个CI类型：服务器和应用
        ci_types = init_ci_types(2)
        server_type = ci_types[0]
        app_type = ci_types[1]
        logger.info(f"创建服务器CI类型: {server_type.name}, ID: {server_type.id}")
        logger.info(f"创建应用CI类型: {app_type.name}, ID: {app_type.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Runs")
        logger.info(f"创建关系类型: Runs, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        type_relation = CITypeRelationManager.add(
            server_type.id,
            app_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {server_type.name} -> {app_type.name}, ID: {type_relation}")

        # 获取CI类型的唯一键属性
        server_unique_attr = AttributeCache.get(server_type.unique_id)
        app_unique_attr = AttributeCache.get(app_type.unique_id)

        # 创建服务器CI实例
        ci_manager = CIManager()
        server_ci_ids = []
        for i in range(3):
            ci_data = {
                server_unique_attr.name: 20001 + i  # 为唯一键提供值
            }
            server_ci_id = ci_manager.add(server_type.name, **ci_data)[0]
            server_ci_ids.append(server_ci_id)
            logger.info(f"创建服务器CI实例 {i+1}: ID={server_ci_id}")

        # 创建应用CI实例
        app_ci_ids = []
        for i in range(2):
            ci_data = {
                app_unique_attr.name: 21001 + i  # 为唯一键提供值
            }
            app_ci_id = ci_manager.add(app_type.name, **ci_data)[0]
            app_ci_ids.append(app_ci_id)
            logger.info(f"创建应用CI实例 {i+1}: ID={app_ci_id}")

        # 只为部分服务器建立与应用的关系
        # 服务器1 -> 应用1
        CIRelationManager.add(server_ci_ids[0], app_ci_ids[0], relation_type_id=relation_type.id)
        logger.info(f"创建关系: 服务器{server_ci_ids[0]} -> 应用{app_ci_ids[0]}")
        
        # 服务器2 -> 应用2
        CIRelationManager.add(server_ci_ids[1], app_ci_ids[1], relation_type_id=relation_type.id)
        logger.info(f"创建关系: 服务器{server_ci_ids[1]} -> 应用{app_ci_ids[1]}")
        
        # 服务器3没有与任何应用的关系，这就是我们要找的缺失关系

        # 准备请求数据 - 查找缺失关系
        request_data = {
            "source": {"type_id": server_type.id},
            "target": {"type_ids": [app_type.id]},
            "path": [[server_type.id, app_type.id]],
            "missing": "down",  # 关键参数：搜索缺失关系
            "page": 1,
            "count": 10
        }

        # 发送POST请求
        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data)
        logger.info(f"缺失关系搜索响应状态码: {response.status_code}")
        logger.info(f"缺失关系搜索响应内容: {response.json}")

        # 验证响应
        assert response.status_code == 200
        assert "paths" in response.json
        assert "numfound" in response.json
        assert "counter" in response.json
        assert "id2ci" in response.json

        # 验证返回的缺失路径
        paths = response.json["paths"]
        assert isinstance(paths, dict)

        # 获取所有缺失路径
        all_missing_paths = []
        for relation_type_name, path_list in paths.items():
            all_missing_paths.extend(path_list)

        # 应该找到至少一个缺失关系（服务器3）
        assert len(all_missing_paths) >= 1

        # 验证缺失路径只包含没有关系的服务器
        missing_server_ids = set()
        for path in all_missing_paths:
            assert len(path) == 1  # 缺失关系路径长度应该为1（只有源CI）
            missing_server_ids.add(int(path[0]))

        # 验证缺失的服务器是预期的（服务器3）
        assert server_ci_ids[2] in missing_server_ids
        # 验证有关系的服务器不在缺失列表中
        assert server_ci_ids[0] not in missing_server_ids
        assert server_ci_ids[1] not in missing_server_ids

        logger.info("两级缺失关系搜索功能测试通过")

    @with_request_context
    def test_search_missing_path_three_level_indirect(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试三级缺失关系搜索功能 - 间接关系缺失"""
        logger.info("开始测试三级缺失关系搜索功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建三个CI类型：服务器、中间件、应用
        ci_types = init_ci_types(3)
        server_type = ci_types[0]
        middleware_type = ci_types[1]
        app_type = ci_types[2]
        logger.info(f"创建服务器CI类型: {server_type.name}, ID: {server_type.id}")
        logger.info(f"创建中间件CI类型: {middleware_type.name}, ID: {middleware_type.id}")
        logger.info(f"创建应用CI类型: {app_type.name}, ID: {app_type.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Deploys")
        logger.info(f"创建关系类型: Deploys, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        server_middleware_relation = CITypeRelationManager.add(
            server_type.id,
            middleware_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {server_type.name} -> {middleware_type.name}, ID: {server_middleware_relation}")

        middleware_app_relation = CITypeRelationManager.add(
            middleware_type.id,
            app_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {middleware_type.name} -> {app_type.name}, ID: {middleware_app_relation}")

        # 获取CI类型的唯一键属性
        server_unique_attr = AttributeCache.get(server_type.unique_id)
        middleware_unique_attr = AttributeCache.get(middleware_type.unique_id)
        app_unique_attr = AttributeCache.get(app_type.unique_id)

        # 创建服务器CI实例
        ci_manager = CIManager()
        server_ci_data = {
            server_unique_attr.name: 22001  # 为唯一键提供值
        }
        server_ci_id = ci_manager.add(server_type.name, **server_ci_data)[0]
        logger.info(f"创建服务器CI实例: ID={server_ci_id}")

        # 创建中间件CI实例
        middleware_ci_ids = []
        for i in range(2):
            ci_data = {
                middleware_unique_attr.name: 23001 + i  # 为唯一键提供值
            }
            middleware_ci_id = ci_manager.add(middleware_type.name, **ci_data)[0]
            middleware_ci_ids.append(middleware_ci_id)
            # 建立服务器到中间件的关系
            CIRelationManager.add(server_ci_id, middleware_ci_id, relation_type_id=relation_type.id)
            logger.info(f"创建中间件CI实例: ID={middleware_ci_id}, 关联到服务器: {server_ci_id}")

        # 创建应用CI实例
        app_ci_ids = []
        for i in range(2):
            ci_data = {
                app_unique_attr.name: 24001 + i  # 为唯一键提供值
            }
            app_ci_id = ci_manager.add(app_type.name, **ci_data)[0]
            app_ci_ids.append(app_ci_id)
            logger.info(f"创建应用CI实例: ID={app_ci_id}")

        # 只为第一个中间件建立与应用的关系
        # 中间件1 -> 应用1
        CIRelationManager.add(middleware_ci_ids[0], app_ci_ids[0], relation_type_id=relation_type.id)
        logger.info(f"创建关系: 中间件{middleware_ci_ids[0]} -> 应用{app_ci_ids[0]}")
        
        # 中间件2没有与任何应用的关系，这就是缺失的最后一层关系

        # 准备请求数据 - 查找三级路径的缺失关系
        request_data = {
            "source": {"type_id": server_type.id},
            "target": {"type_ids": [app_type.id]},
            "path": [[server_type.id, middleware_type.id, app_type.id]],
            "missing": "down",
            "page": 1,
            "count": 10
        }

        # 发送POST请求
        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data)
        logger.info(f"三级缺失关系搜索响应状态码: {response.status_code}")
        logger.info(f"三级缺失关系搜索响应内容: {response.json}")

        # 验证响应
        assert response.status_code == 200
        assert "paths" in response.json
        assert "numfound" in response.json

        # 验证返回的缺失路径
        paths = response.json["paths"]
        assert isinstance(paths, dict)

        # 获取所有缺失路径
        all_missing_paths = []
        for relation_type_name, path_list in paths.items():
            all_missing_paths.extend(path_list)

        # 应该找到至少一个缺失关系路径
        assert len(all_missing_paths) >= 1

        # 验证缺失路径的结构
        for path in all_missing_paths:
            assert len(path) == 2  # 缺失最后一层关系的路径长度应该为2（服务器->中间件）
            assert int(path[0]) == server_ci_id  # 第一个应该是服务器
            assert int(path[1]) in middleware_ci_ids  # 第二个应该是中间件之一

        # 验证id2ci包含正确的CI信息
        id2ci = response.json["id2ci"]
        assert str(server_ci_id) in id2ci
        for middleware_id in middleware_ci_ids:
            if any(int(path[1]) == middleware_id for path in all_missing_paths):
                assert str(middleware_id) in id2ci

        logger.info("三级缺失关系搜索功能测试通过")

    @with_request_context
    def test_search_missing_path_with_query_filter(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试带查询条件的缺失关系搜索功能"""
        logger.info("开始测试带查询条件的缺失关系搜索功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建两个CI类型
        ci_types = init_ci_types(2)
        server_type = ci_types[0]
        app_type = ci_types[1]
        logger.info(f"创建服务器CI类型: {server_type.name}, ID: {server_type.id}")
        logger.info(f"创建应用CI类型: {app_type.name}, ID: {app_type.id}")

        # 创建额外的属性用于查询过滤
        status_attr = init_attributes(1, value_type="2")[0]  # TEXT类型
        CITypeAttributeManager.add(app_type.id, [status_attr.id])
        logger.info(f"创建状态属性: {status_attr.name}, ID: {status_attr.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Hosts")
        logger.info(f"创建关系类型: Hosts, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        type_relation = CITypeRelationManager.add(
            server_type.id,
            app_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {server_type.name} -> {app_type.name}, ID: {type_relation}")

        # 获取CI类型的唯一键属性
        server_unique_attr = AttributeCache.get(server_type.unique_id)
        app_unique_attr = AttributeCache.get(app_type.unique_id)

        # 创建服务器CI实例
        ci_manager = CIManager()
        server_ci_ids = []
        for i in range(2):
            ci_data = {
                server_unique_attr.name: 25001 + i  # 为唯一键提供值
            }
            server_ci_id = ci_manager.add(server_type.name, **ci_data)[0]
            server_ci_ids.append(server_ci_id)
            logger.info(f"创建服务器CI实例 {i+1}: ID={server_ci_id}")

        # 创建应用CI实例 - 不同状态
        app_ci_ids = []
        status_values = ["active", "inactive"]

        for i, status in enumerate(status_values):
            app_ci_data = {
                app_unique_attr.name: 26001 + i,  # 为唯一键提供值
                status_attr.name: status
            }
            app_ci_id = ci_manager.add(app_type.name, **app_ci_data)[0]
            app_ci_ids.append(app_ci_id)
            logger.info(f"创建应用CI实例: ID={app_ci_id}, 状态={status}")

        # 只为一个服务器建立与active应用的关系
        # 服务器1 -> active应用
        CIRelationManager.add(server_ci_ids[0], app_ci_ids[0], relation_type_id=relation_type.id)
        logger.info(f"创建关系: 服务器{server_ci_ids[0]} -> active应用{app_ci_ids[0]}")

        # 服务器2没有与任何active应用的关系，但可能与inactive应用有关系
        CIRelationManager.add(server_ci_ids[1], app_ci_ids[1], relation_type_id=relation_type.id)
        logger.info(f"创建关系: 服务器{server_ci_ids[1]} -> inactive应用{app_ci_ids[1]}")

        # 准备请求数据 - 查找缺失与active应用关系的服务器
        request_data = {
            "source": {"type_id": server_type.id},
            "target": {
                "type_ids": [app_type.id],
                "q": f"{status_attr.name}:active"  # 只查询active状态的应用
            },
            "path": [[server_type.id, app_type.id]],
            "missing": "down",  # 搜索缺失关系
            "page": 1,
            "count": 10
        }

        # 发送POST请求
        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data)
        logger.info(f"带查询条件的缺失关系搜索响应状态码: {response.status_code}")
        logger.info(f"带查询条件的缺失关系搜索响应内容: {response.json}")

        # 验证响应
        assert response.status_code == 200
        assert "paths" in response.json

        # 验证返回的缺失路径
        paths = response.json["paths"]
        assert isinstance(paths, dict)

        # 获取所有缺失路径
        all_missing_paths = []
        for relation_type_name, path_list in paths.items():
            all_missing_paths.extend(path_list)

        # 应该找到服务器2（没有与active应用的关系）
        assert len(all_missing_paths) >= 1

        # 验证缺失路径包含正确的服务器
        missing_server_ids = set()
        for path in all_missing_paths:
            assert len(path) == 1  # 缺失关系路径长度应该为1
            missing_server_ids.add(int(path[0]))

        # 服务器2应该在缺失列表中（没有与active应用的关系）
        assert server_ci_ids[1] in missing_server_ids
        # 服务器1不应该在缺失列表中（有与active应用的关系）
        assert server_ci_ids[0] not in missing_server_ids

        logger.info("带查询条件的缺失关系搜索功能测试通过")

    @with_request_context
    def test_search_missing_path_pagination(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试缺失关系搜索的分页功能"""
        logger.info("开始测试缺失关系搜索的分页功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建两个CI类型
        ci_types = init_ci_types(2)
        server_type = ci_types[0]
        app_type = ci_types[1]
        logger.info(f"创建服务器CI类型: {server_type.name}, ID: {server_type.id}")
        logger.info(f"创建应用CI类型: {app_type.name}, ID: {app_type.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Contains")
        logger.info(f"创建关系类型: Contains, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        type_relation = CITypeRelationManager.add(
            server_type.id,
            app_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {server_type.name} -> {app_type.name}, ID: {type_relation}")

        # 获取CI类型的唯一键属性
        server_unique_attr = AttributeCache.get(server_type.unique_id)
        app_unique_attr = AttributeCache.get(app_type.unique_id)

        # 创建多个服务器CI实例（都没有关系，作为缺失关系的测试数据）
        ci_manager = CIManager()
        server_ci_ids = []
        for i in range(5):  # 创建5个服务器，都没有关系
            ci_data = {
                server_unique_attr.name: 27001 + i  # 为唯一键提供值
            }
            server_ci_id = ci_manager.add(server_type.name, **ci_data)[0]
            server_ci_ids.append(server_ci_id)
            logger.info(f"创建服务器CI实例 {i+1}: ID={server_ci_id}")

        # 创建一个应用CI（但不建立关系）
        app_ci_data = {
            app_unique_attr.name: 28001  # 为唯一键提供值
        }
        app_ci_id = ci_manager.add(app_type.name, **app_ci_data)[0]
        logger.info(f"创建应用CI实例: ID={app_ci_id}")

        # 测试第一页 - 限制每页2条
        request_data_page1 = {
            "source": {"type_id": server_type.id},
            "target": {"type_ids": [app_type.id]},
            "path": [[server_type.id, app_type.id]],
            "missing": "down",  # 搜索缺失关系
            "page": 1,
            "count": 2
        }

        response_page1 = client.post('/api/v0.1/ci_relations/path/s', json=request_data_page1)
        logger.info(f"缺失关系第一页响应状态码: {response_page1.status_code}")
        logger.info(f"缺失关系第一页响应内容: {response_page1.json}")

        # 验证第一页响应
        assert response_page1.status_code == 200
        assert "paths" in response_page1.json
        assert "numfound" in response_page1.json
        assert response_page1.json["page"] == 1

        paths_page1 = response_page1.json["paths"]
        numfound = response_page1.json["numfound"]
        
        # 获取第一页的所有缺失路径
        all_missing_paths_page1 = []
        if isinstance(paths_page1, dict):
            for relation_type_name, path_list in paths_page1.items():
                all_missing_paths_page1.extend(path_list)
        
        # 验证总数和第一页数据
        assert numfound >= 5  # 至少有5个缺失关系
        assert len(all_missing_paths_page1) <= 2  # 第一页最多2条

        # 测试第二页
        request_data_page2 = {
            "source": {"type_id": server_type.id},
            "target": {"type_ids": [app_type.id]},
            "path": [[server_type.id, app_type.id]],
            "missing": "down",  # 搜索缺失关系
            "page": 2,
            "count": 2
        }

        response_page2 = client.post('/api/v0.1/ci_relations/path/s', json=request_data_page2)
        logger.info(f"缺失关系第二页响应状态码: {response_page2.status_code}")
        logger.info(f"缺失关系第二页响应内容: {response_page2.json}")

        # 验证第二页响应
        assert response_page2.status_code == 200
        assert "paths" in response_page2.json
        assert response_page2.json["page"] == 2
        assert response_page2.json["numfound"] == numfound  # 总数应该一致

        paths_page2 = response_page2.json["paths"]
        
        # 获取第二页的所有缺失路径
        all_missing_paths_page2 = []
        if isinstance(paths_page2, dict):
            for relation_type_name, path_list in paths_page2.items():
                all_missing_paths_page2.extend(path_list)
        
        assert len(all_missing_paths_page2) <= 2  # 第二页最多2条

        # 验证两页的数据不重复（如果有足够的数据）
        if len(all_missing_paths_page1) > 0 and len(all_missing_paths_page2) > 0:
            page1_paths_set = {tuple(path) for path in all_missing_paths_page1}
            page2_paths_set = {tuple(path) for path in all_missing_paths_page2}
            assert page1_paths_set.isdisjoint(page2_paths_set), "分页数据不应该重复"

        logger.info("缺失关系搜索的分页功能测试通过")

    @with_request_context
    def test_search_missing_path_invalid_parameters(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试缺失关系搜索的参数验证"""
        logger.info("开始测试缺失关系搜索的参数验证")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 测试missing参数为false的情况（应该调用正常搜索）
        request_data_missing_false = {
            "source": {"type_id": 1},
            "target": {"type_ids": [2]},
            "path": [[1, 2]],
        }

        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data_missing_false)
        logger.info(f"missing没有参数时响应状态码: {response.status_code}")
        # 这应该调用正常的搜索路径，可能会因为没有数据而返回错误或空结果
        assert response.status_code in [200, 400, 500]

        # 测试missing参数为字符串"true"的情况
        request_data_missing_string = {
            "source": {"type_id": 1},
            "target": {"type_ids": [2]},
            "path": [[1, 2]],
            "missing": "true"  # 字符串格式
        }

        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data_missing_string)
        logger.info(f"missing='true'时响应状态码: {response.status_code}")
        # 这应该被正确解析为true并调用缺失关系搜索
        assert response.status_code in [200, 400, 500]

        # 测试不提供missing参数的情况（默认为false）
        request_data_no_missing = {
            "source": {"type_id": 1},
            "target": {"type_ids": [2]},
            "path": [[1, 2]]
            # 没有missing参数
        }

        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data_no_missing)
        logger.info(f"不提供missing参数时响应状态码: {response.status_code}")
        # 默认应该调用正常搜索
        assert response.status_code in [200, 400, 500]

        logger.info("缺失关系搜索的参数验证测试通过")

    @with_request_context
    def test_search_missing_path_empty_result(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试缺失关系搜索没有找到缺失关系的情况"""
        logger.info("开始测试缺失关系搜索没有缺失关系的情况")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建两个CI类型
        ci_types = init_ci_types(2)
        server_type = ci_types[0]
        app_type = ci_types[1]
        logger.info(f"创建服务器CI类型: {server_type.name}, ID: {server_type.id}")
        logger.info(f"创建应用CI类型: {app_type.name}, ID: {app_type.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Serves")
        logger.info(f"创建关系类型: Serves, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        type_relation = CITypeRelationManager.add(
            server_type.id,
            app_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {server_type.name} -> {app_type.name}, ID: {type_relation}")

        # 获取CI类型的唯一键属性
        server_unique_attr = AttributeCache.get(server_type.unique_id)
        app_unique_attr = AttributeCache.get(app_type.unique_id)

        # 创建服务器和应用CI实例，并建立完整的关系
        ci_manager = CIManager()
        server_ci_data = {
            server_unique_attr.name: 29001  # 为唯一键提供值
        }
        server_ci_id = ci_manager.add(server_type.name, **server_ci_data)[0]
        logger.info(f"创建服务器CI实例: ID={server_ci_id}")

        app_ci_data = {
            app_unique_attr.name: 30001  # 为唯一键提供值
        }
        app_ci_id = ci_manager.add(app_type.name, **app_ci_data)[0]
        logger.info(f"创建应用CI实例: ID={app_ci_id}")

        # 建立完整的关系（没有缺失）
        CIRelationManager.add(server_ci_id, app_ci_id, relation_type_id=relation_type.id)
        logger.info(f"创建关系: 服务器{server_ci_id} -> 应用{app_ci_id}")

        # 准备请求数据 - 查找缺失关系（但实际上没有缺失）
        request_data = {
            "source": {"type_id": server_type.id},
            "target": {"type_ids": [app_type.id]},
            "path": [[server_type.id, app_type.id]],
            "missing": "down",  # 搜索缺失关系
            "page": 1,
            "count": 10
        }

        # 发送POST请求
        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data)
        logger.info(f"无缺失关系搜索响应状态码: {response.status_code}")
        logger.info(f"无缺失关系搜索响应内容: {response.json}")

        # 验证响应
        assert response.status_code == 200
        assert "paths" in response.json
        assert "numfound" in response.json

        # 验证没有找到缺失关系
        paths = response.json["paths"]
        numfound = response.json["numfound"]
        
        # 应该没有缺失关系
        assert numfound == 0

        # paths应该为空或者包含空的路径列表
        if isinstance(paths, dict):
            for relation_type_name, path_list in paths.items():
                assert len(path_list) == 0

        logger.info("缺失关系搜索没有缺失关系的情况测试通过")

    @with_request_context
    def test_search_missing_path_upstream_two_level_direct(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试两级上游缺失关系搜索功能 - 直接关系缺失"""
        logger.info("开始测试两级上游缺失关系搜索功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建两个CI类型：服务器和应用
        ci_types = init_ci_types(2)
        server_type = ci_types[0]
        app_type = ci_types[1]
        logger.info(f"创建服务器CI类型: {server_type.name}, ID: {server_type.id}")
        logger.info(f"创建应用CI类型: {app_type.name}, ID: {app_type.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Hosts")
        logger.info(f"创建关系类型: Hosts, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        type_relation = CITypeRelationManager.add(
            server_type.id,
            app_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {server_type.name} -> {app_type.name}, ID: {type_relation}")

        # 获取CI类型的唯一键属性
        server_unique_attr = AttributeCache.get(server_type.unique_id)
        app_unique_attr = AttributeCache.get(app_type.unique_id)

        # 创建服务器CI实例
        ci_manager = CIManager()
        server_ci_ids = []
        for i in range(2):
            ci_data = {
                server_unique_attr.name: 31001 + i  # 为唯一键提供值
            }
            server_ci_id = ci_manager.add(server_type.name, **ci_data)[0]
            server_ci_ids.append(server_ci_id)
            logger.info(f"创建服务器CI实例 {i+1}: ID={server_ci_id}")

        # 创建应用CI实例
        app_ci_ids = []
        for i in range(3):
            ci_data = {
                app_unique_attr.name: 32001 + i  # 为唯一键提供值
            }
            app_ci_id = ci_manager.add(app_type.name, **ci_data)[0]
            app_ci_ids.append(app_ci_id)
            logger.info(f"创建应用CI实例 {i+1}: ID={app_ci_id}")

        # 只为部分应用建立与服务器的关系
        # 服务器1 -> 应用1
        CIRelationManager.add(server_ci_ids[0], app_ci_ids[0], relation_type_id=relation_type.id)
        logger.info(f"创建关系: 服务器{server_ci_ids[0]} -> 应用{app_ci_ids[0]}")
        
        # 服务器2 -> 应用2
        CIRelationManager.add(server_ci_ids[1], app_ci_ids[1], relation_type_id=relation_type.id)
        logger.info(f"创建关系: 服务器{server_ci_ids[1]} -> 应用{app_ci_ids[1]}")
        
        # 应用3没有与任何服务器的关系，这就是我们要找的上游缺失关系

        # 准备请求数据 - 查找上游缺失关系
        request_data = {
            "source": {"type_id": server_type.id},
            "target": {"type_ids": [app_type.id]},
            "path": [[server_type.id, app_type.id]],
            "missing": "up",  # 关键参数：搜索上游缺失关系
            "page": 1,
            "count": 10
        }

        # 发送POST请求
        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data)
        logger.info(f"上游缺失关系搜索响应状态码: {response.status_code}")
        logger.info(f"上游缺失关系搜索响应内容: {response.json}")

        # 验证响应
        assert response.status_code == 200
        assert "paths" in response.json
        assert "numfound" in response.json
        assert "counter" in response.json
        assert "id2ci" in response.json

        # 验证返回的上游缺失路径
        paths = response.json["paths"]
        assert isinstance(paths, dict)

        # 获取所有缺失路径
        all_missing_paths = []
        for relation_type_name, path_list in paths.items():
            all_missing_paths.extend(path_list)

        # 应该找到至少一个上游缺失关系（应用3）
        assert len(all_missing_paths) >= 1

        # 验证缺失路径只包含没有上游关系的应用
        missing_app_ids = set()
        for path in all_missing_paths:
            assert len(path) == 1  # 上游缺失关系路径长度应该为1（只有目标CI）
            missing_app_ids.add(int(path[0]))

        # 验证缺失的应用是预期的（应用3）
        assert app_ci_ids[2] in missing_app_ids
        # 验证有关系的应用不在缺失列表中
        assert app_ci_ids[0] not in missing_app_ids
        assert app_ci_ids[1] not in missing_app_ids

        logger.info("两级上游缺失关系搜索功能测试通过")

    @with_request_context
    def test_search_missing_path_upstream_three_level_indirect(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试三级上游缺失关系搜索功能 - 间接关系缺失"""
        logger.info("开始测试三级上游缺失关系搜索功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建三个CI类型：服务器、中间件、应用
        ci_types = init_ci_types(3)
        server_type = ci_types[0]
        middleware_type = ci_types[1]
        app_type = ci_types[2]
        logger.info(f"创建服务器CI类型: {server_type.name}, ID: {server_type.id}")
        logger.info(f"创建中间件CI类型: {middleware_type.name}, ID: {middleware_type.id}")
        logger.info(f"创建应用CI类型: {app_type.name}, ID: {app_type.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Supports")
        logger.info(f"创建关系类型: Supports, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        server_middleware_relation = CITypeRelationManager.add(
            server_type.id,
            middleware_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {server_type.name} -> {middleware_type.name}, ID: {server_middleware_relation}")

        middleware_app_relation = CITypeRelationManager.add(
            middleware_type.id,
            app_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {middleware_type.name} -> {app_type.name}, ID: {middleware_app_relation}")

        # 获取CI类型的唯一键属性
        server_unique_attr = AttributeCache.get(server_type.unique_id)
        middleware_unique_attr = AttributeCache.get(middleware_type.unique_id)
        app_unique_attr = AttributeCache.get(app_type.unique_id)

        # 创建服务器CI实例
        ci_manager = CIManager()
        server_ci_data = {
            server_unique_attr.name: 33001  # 为唯一键提供值
        }
        server_ci_id = ci_manager.add(server_type.name, **server_ci_data)[0]
        logger.info(f"创建服务器CI实例: ID={server_ci_id}")

        # 创建中间件CI实例
        middleware_ci_ids = []
        for i in range(2):
            ci_data = {
                middleware_unique_attr.name: 34001 + i  # 为唯一键提供值
            }
            middleware_ci_id = ci_manager.add(middleware_type.name, **ci_data)[0]
            middleware_ci_ids.append(middleware_ci_id)
            # 建立服务器到中间件的关系
            CIRelationManager.add(server_ci_id, middleware_ci_id, relation_type_id=relation_type.id)
            logger.info(f"创建中间件CI实例: ID={middleware_ci_id}, 关联到服务器: {server_ci_id}")

        # 创建应用CI实例
        app_ci_ids = []
        for i in range(3):
            ci_data = {
                app_unique_attr.name: 35001 + i  # 为唯一键提供值
            }
            app_ci_id = ci_manager.add(app_type.name, **ci_data)[0]
            app_ci_ids.append(app_ci_id)
            logger.info(f"创建应用CI实例: ID={app_ci_id}")

        # 只为部分应用建立与中间件的关系
        # 中间件1 -> 应用1
        CIRelationManager.add(middleware_ci_ids[0], app_ci_ids[0], relation_type_id=relation_type.id)
        logger.info(f"创建关系: 中间件{middleware_ci_ids[0]} -> 应用{app_ci_ids[0]}")
        
        # 中间件2 -> 应用2  
        CIRelationManager.add(middleware_ci_ids[1], app_ci_ids[1], relation_type_id=relation_type.id)
        logger.info(f"创建关系: 中间件{middleware_ci_ids[1]} -> 应用{app_ci_ids[1]}")

        # 应用3没有与任何中间件的关系，因此也没有完整的上游路径

        # 准备请求数据 - 查找三级路径的上游缺失关系
        request_data = {
            "source": {"type_id": server_type.id},
            "target": {"type_ids": [app_type.id]},
            "path": [[server_type.id, middleware_type.id, app_type.id]],
            "missing": "up",
            "page": 1,
            "count": 10
        }

        # 发送POST请求
        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data)
        logger.info(f"三级上游缺失关系搜索响应状态码: {response.status_code}")
        logger.info(f"三级上游缺失关系搜索响应内容: {response.json}")

        # 验证响应
        assert response.status_code == 200
        assert "paths" in response.json
        assert "numfound" in response.json

        # 验证返回的上游缺失路径
        paths = response.json["paths"]
        assert isinstance(paths, dict)

        # 获取所有缺失路径
        all_missing_paths = []
        for relation_type_name, path_list in paths.items():
            all_missing_paths.extend(path_list)

        # 应该找到至少一个上游缺失关系路径
        assert len(all_missing_paths) >= 1

        # 验证缺失路径的结构
        for path in all_missing_paths:
            assert len(path) == 1  # 上游缺失关系路径长度应该为1（只有目标应用）
            assert int(path[0]) in app_ci_ids  # 应该是应用CI之一

        # 验证缺失的应用是没有完整上游路径的应用3
        missing_app_ids = {int(path[0]) for path in all_missing_paths}
        assert app_ci_ids[2] in missing_app_ids  # 应用3应该在缺失列表中

        # 验证id2ci包含正确的CI信息
        id2ci = response.json["id2ci"]
        for app_id in missing_app_ids:
            assert str(app_id) in id2ci

        logger.info("三级上游缺失关系搜索功能测试通过")

    @with_request_context
    def test_search_missing_path_upstream_with_query_filter(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试带查询条件的上游缺失关系搜索功能"""
        logger.info("开始测试带查询条件的上游缺失关系搜索功能")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建两个CI类型
        ci_types = init_ci_types(2)
        server_type = ci_types[0]
        app_type = ci_types[1]
        logger.info(f"创建服务器CI类型: {server_type.name}, ID: {server_type.id}")
        logger.info(f"创建应用CI类型: {app_type.name}, ID: {app_type.id}")

        # 创建额外的属性用于查询过滤
        status_attr = init_attributes(1, value_type="2")[0]  # TEXT类型
        CITypeAttributeManager.add(app_type.id, [status_attr.id])
        logger.info(f"创建状态属性: {status_attr.name}, ID: {status_attr.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Deploys")
        logger.info(f"创建关系类型: Deploys, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        type_relation = CITypeRelationManager.add(
            server_type.id,
            app_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {server_type.name} -> {app_type.name}, ID: {type_relation}")

        # 获取CI类型的唯一键属性
        server_unique_attr = AttributeCache.get(server_type.unique_id)
        app_unique_attr = AttributeCache.get(app_type.unique_id)

        # 创建服务器CI实例
        ci_manager = CIManager()
        server_ci_ids = []
        for i in range(2):
            ci_data = {
                server_unique_attr.name: 36001 + i  # 为唯一键提供值
            }
            server_ci_id = ci_manager.add(server_type.name, **ci_data)[0]
            server_ci_ids.append(server_ci_id)
            logger.info(f"创建服务器CI实例 {i+1}: ID={server_ci_id}")

        # 创建应用CI实例 - 不同状态
        app_ci_ids = []
        status_values = ["active", "inactive", "active"]

        for i, status in enumerate(status_values):
            app_ci_data = {
                app_unique_attr.name: 37001 + i,  # 为唯一键提供值
                status_attr.name: status
            }
            app_ci_id = ci_manager.add(app_type.name, **app_ci_data)[0]
            app_ci_ids.append(app_ci_id)
            logger.info(f"创建应用CI实例: ID={app_ci_id}, 状态={status}")

        # 只为一个active应用建立与服务器的关系
        # 服务器1 -> 第一个active应用（应用1）
        CIRelationManager.add(server_ci_ids[0], app_ci_ids[0], relation_type_id=relation_type.id)
        logger.info(f"创建关系: 服务器{server_ci_ids[0]} -> active应用{app_ci_ids[0]}")

        # 第二个inactive应用（应用2）没有与任何服务器的关系

        # 第三个active应用（应用3）没有与任何服务器的关系

        # 准备请求数据 - 查找缺失与active应用上游关系的情况
        request_data = {
            "source": {"type_id": server_type.id},
            "target": {
                "type_ids": [app_type.id],
                "q": f"{status_attr.name}:active"  # 只查询active状态的应用
            },
            "path": [[server_type.id, app_type.id]],
            "missing": "up",  # 搜索上游缺失关系
            "page": 1,
            "count": 10
        }

        # 发送POST请求
        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data)
        logger.info(f"带查询条件的上游缺失关系搜索响应状态码: {response.status_code}")
        logger.info(f"带查询条件的上游缺失关系搜索响应内容: {response.json}")

        # 验证响应
        assert response.status_code == 200
        assert "paths" in response.json

        # 验证返回的上游缺失路径
        paths = response.json["paths"]
        assert isinstance(paths, dict)

        # 获取所有缺失路径
        all_missing_paths = []
        for relation_type_name, path_list in paths.items():
            all_missing_paths.extend(path_list)

        # 应该找到第三个active应用（没有与服务器的关系）
        assert len(all_missing_paths) >= 1

        # 验证缺失路径包含正确的应用
        missing_app_ids = set()
        for path in all_missing_paths:
            assert len(path) == 1  # 上游缺失关系路径长度应该为1
            missing_app_ids.add(int(path[0]))

        # 第三个active应用应该在缺失列表中（没有与服务器的关系）
        assert app_ci_ids[2] in missing_app_ids
        # 第一个active应用不应该在缺失列表中（有与服务器的关系）
        assert app_ci_ids[0] not in missing_app_ids
        # inactive应用不应该在结果中（被查询条件过滤）
        assert app_ci_ids[1] not in missing_app_ids

        logger.info("带查询条件的上游缺失关系搜索功能测试通过")

    @with_request_context
    def test_search_missing_path_upstream_empty_result(self, client: FlaskClient, db_session, auth_user, celery_worker):
        """测试上游缺失关系搜索没有找到缺失关系的情况"""
        logger.info("开始测试上游缺失关系搜索没有缺失关系的情况")

        # 设置管理员用户
        user = auth_user('admin')
        assert user is not None
        assert current_user.is_authenticated

        # 创建两个CI类型
        ci_types = init_ci_types(2)
        server_type = ci_types[0]
        app_type = ci_types[1]
        logger.info(f"创建服务器CI类型: {server_type.name}, ID: {server_type.id}")
        logger.info(f"创建应用CI类型: {app_type.name}, ID: {app_type.id}")

        # 创建关系类型
        relation_type = RelationTypeManager.add("Manages")
        logger.info(f"创建关系类型: Manages, ID: {relation_type.id}")

        # 创建CI类型间的关系定义
        type_relation = CITypeRelationManager.add(
            server_type.id,
            app_type.id,
            relation_type.id,
            ConstraintEnum.One2Many,
            parent_attr_ids=[],
            child_attr_ids=[]
        )
        logger.info(f"创建CI类型关系: {server_type.name} -> {app_type.name}, ID: {type_relation}")

        # 获取CI类型的唯一键属性
        server_unique_attr = AttributeCache.get(server_type.unique_id)
        app_unique_attr = AttributeCache.get(app_type.unique_id)

        # 创建服务器和应用CI实例，并建立完整的关系
        ci_manager = CIManager()
        server_ci_data = {
            server_unique_attr.name: 38001  # 为唯一键提供值
        }
        server_ci_id = ci_manager.add(server_type.name, **server_ci_data)[0]
        logger.info(f"创建服务器CI实例: ID={server_ci_id}")

        app_ci_data = {
            app_unique_attr.name: 39001  # 为唯一键提供值
        }
        app_ci_id = ci_manager.add(app_type.name, **app_ci_data)[0]
        logger.info(f"创建应用CI实例: ID={app_ci_id}")

        # 建立完整的关系（没有上游缺失）
        CIRelationManager.add(server_ci_id, app_ci_id, relation_type_id=relation_type.id)
        logger.info(f"创建关系: 服务器{server_ci_id} -> 应用{app_ci_id}")

        # 准备请求数据 - 查找上游缺失关系（但实际上没有缺失）
        request_data = {
            "source": {"type_id": server_type.id},
            "target": {"type_ids": [app_type.id]},
            "path": [[server_type.id, app_type.id]],
            "missing": "up",  # 搜索上游缺失关系
            "page": 1,
            "count": 10
        }

        # 发送POST请求
        response = client.post('/api/v0.1/ci_relations/path/s', json=request_data)
        logger.info(f"无上游缺失关系搜索响应状态码: {response.status_code}")
        logger.info(f"无上游缺失关系搜索响应内容: {response.json}")

        # 验证响应
        assert response.status_code == 200
        assert "paths" in response.json
        assert "numfound" in response.json

        # 验证没有找到上游缺失关系
        paths = response.json["paths"]
        numfound = response.json["numfound"]
        
        # 应该没有上游缺失关系
        assert numfound == 0

        # paths应该为空或者包含空的路径列表
        if isinstance(paths, dict):
            for relation_type_name, path_list in paths.items():
                assert len(path_list) == 0

        logger.info("上游缺失关系搜索没有缺失关系的情况测试通过")
