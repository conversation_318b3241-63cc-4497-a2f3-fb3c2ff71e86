# -*- coding:utf-8 -*-
from collections import Counter
from collections import defaultdict
from collections import namedtuple

import copy
import json
import networkx as nx
import sys
import time
from flask import abort
from flask import current_app
from flask_login import current_user

from api.extensions import rd
from api.lib.cmdb.cache import AttributeCache
from api.lib.cmdb.cache import CITypeCache
from api.lib.cmdb.ci import CIRelationManager
from api.lib.cmdb.ci_type import CITypeRelationManager
from api.lib.cmdb.const import ConstraintEnum
from api.lib.cmdb.const import PermEnum
from api.lib.cmdb.const import REDIS_PREFIX_CI_RELATION
from api.lib.cmdb.const import REDIS_PREFIX_CI_RELATION2
from api.lib.cmdb.const import ResourceTypeEnum
from api.lib.cmdb.perms import CIFilterPermsCRUD
from api.lib.cmdb.resp_format import ErrFormat
from api.lib.cmdb.search.ci.db.search import Search as SearchFromDB
from api.lib.cmdb.search.ci.es.search import Search as SearchFromES
from api.lib.cmdb.utils import TableMap
from api.lib.cmdb.utils import ValueTypeMap
from api.lib.perm.acl.acl import ACLManager
from api.lib.perm.acl.acl import is_app_admin
from api.models.cmdb import CI
from api.models.cmdb import CITypeRelation
from api.models.cmdb import RelationType

# 定义搜索结果数据结构
SearchPathResult = namedtuple('SearchPathResult', [
    'response', 'counter', 'total', 'page', 'numfound', 
    'id2ci', 'relation_types', 'type2show_key', 'type2multishow_key'
])

class Search(object):
    def __init__(self, root_id=None,
                 level=None,
                 query=None,
                 fl=None,
                 facet_field=None,
                 page=1,
                 count=None,
                 sort=None,
                 reverse=False,
                 ancestor_ids=None,
                 descendant_ids=None,
                 has_m2m=None,
                 root_parent_path=None):
        self.orig_query = query
        self.fl = fl
        self.facet_field = facet_field
        self.page = page
        self.count = count or current_app.config.get("DEFAULT_PAGE_COUNT")
        self.sort = sort or ("ci_id" if current_app.config.get("USE_ES") else None)

        self.root_id = root_id
        self.level = level or 0
        self.reverse = reverse

        self.level2constraint = CITypeRelationManager.get_level2constraint(
            root_id[0] if root_id and isinstance(root_id, list) else root_id,
            level[0] if isinstance(level, list) and level else level)

        self.ancestor_ids = ancestor_ids
        self.descendant_ids = descendant_ids
        self.root_parent_path = root_parent_path
        self.has_m2m = has_m2m or False
        if not self.has_m2m:
            if self.ancestor_ids:
                self.has_m2m = True
            else:
                level = level[0] if isinstance(level, list) and level else level
                for _l, c in self.level2constraint.items():
                    if _l < int(level) and c == ConstraintEnum.Many2Many:
                        self.has_m2m = True

        self.type2filter_perms = {}

        self.is_app_admin = is_app_admin('cmdb') or current_user.username == "worker"

    def _get_ids(self, ids):

        merge_ids = []
        key = []
        _tmp = []
        for level in range(1, sorted(self.level)[-1] + 1):
            if len(self.descendant_ids or []) >= level and self.type2filter_perms.get(self.descendant_ids[level - 1]):
                id_filter_limit, _ = self._get_ci_filter(self.type2filter_perms[self.descendant_ids[level - 1]])
            else:
                id_filter_limit = {}

            if not self.has_m2m:
                key, prefix = list(map(str, ids)), REDIS_PREFIX_CI_RELATION

            else:
                if not self.ancestor_ids:
                    if level == 1:
                        key, prefix = list(map(str, ids)), REDIS_PREFIX_CI_RELATION
                    else:
                        key = list(set(["{},{}".format(i, j) for idx, i in enumerate(key) for j in _tmp[idx]]))
                        prefix = REDIS_PREFIX_CI_RELATION2
                else:
                    if level == 1:
                        key, prefix = ["{},{}".format(self.ancestor_ids, i) for i in ids], REDIS_PREFIX_CI_RELATION2
                    else:
                        key = list(set(["{},{}".format(i, j) for idx, i in enumerate(key) for j in _tmp[idx]]))
                        prefix = REDIS_PREFIX_CI_RELATION2

            if not key or id_filter_limit is None:
                return []

            res = [json.loads(x).items() for x in [i or '{}' for i in rd.get(key, prefix) or []]]
            _tmp = [[i[0] for i in x if (not id_filter_limit or (
                    key[idx] not in id_filter_limit or int(i[0]) in id_filter_limit[key[idx]]) or
                                         int(i[0]) in id_filter_limit)] for idx, x in enumerate(res)]

            ids = [j for i in _tmp for j in i]

            if level in self.level:
                merge_ids.extend(ids)

        return merge_ids

    def _get_reverse_ids(self, ids):
        merge_ids = []
        level2ids = {}
        for level in range(1, sorted(self.level)[-1] + 1):
            ids, _level2ids = CIRelationManager.get_ancestor_ids(ids, 1)

            if _level2ids.get(2):
                level2ids[level + 1] = _level2ids[2]

            if level in self.level:
                if level in level2ids and level2ids[level]:
                    merge_ids.extend(set(ids) & set(level2ids[level]))
                else:
                    merge_ids.extend(ids)

        return merge_ids

    def _has_read_perm_from_parent_nodes(self):
        self.root_parent_path = list(map(str, self.root_parent_path))
        if str(self.root_id).isdigit() and str(self.root_id) not in self.root_parent_path:
            self.root_parent_path.append(str(self.root_id))
        self.root_parent_path = set(self.root_parent_path)

        if self.is_app_admin:
            self.type2filter_perms = {}
            return True

        res = ACLManager().get_resources(ResourceTypeEnum.CI_FILTER) or {}
        self.type2filter_perms = CIFilterPermsCRUD().get_by_ids(list(map(int, [i['name'] for i in res]))) or {}
        for _, filters in self.type2filter_perms.items():
            if set((filters.get('id_filter') or {}).keys()) & self.root_parent_path:
                return True

        return True

    def search(self, only_ids=False):
        use_ci_filter = len(self.descendant_ids or []) == self.level[0] - 1
        parent_node_perm_passed = not self.is_app_admin and self._has_read_perm_from_parent_nodes()

        ids = [self.root_id] if not isinstance(self.root_id, list) else self.root_id
        cis = [CI.get_by_id(_id) or abort(404, ErrFormat.ci_not_found.format("id={}".format(_id))) for _id in ids]

        merge_ids = self._get_ids(ids) if not self.reverse else self._get_reverse_ids(ids)

        if not self.orig_query or ("_type:" not in self.orig_query
                                   and "type_id:" not in self.orig_query
                                   and "ci_type:" not in self.orig_query):
            type_ids = []
            for level in self.level:
                for ci in cis:
                    if not self.reverse:
                        type_ids.extend(CITypeRelationManager.get_child_type_ids(ci.type_id, level))
                    else:
                        type_ids.extend(CITypeRelationManager.get_parent_type_ids(ci.type_id, level))
            type_ids = set(type_ids)
            if self.orig_query:
                self.orig_query = "_type:({0}),{1}".format(";".join(map(str, type_ids)), self.orig_query)
            else:
                self.orig_query = "_type:({0})".format(";".join(map(str, type_ids)))

        if not merge_ids:
            # cis, counter, total, self.page, numfound, facet_
            return [], {}, 0, self.page, 0, {}

        if current_app.config.get("USE_ES"):
            return SearchFromES(self.orig_query,
                                fl=self.fl,
                                facet_field=self.facet_field,
                                page=self.page,
                                count=self.count,
                                sort=self.sort,
                                ci_ids=merge_ids).search()
        else:
            return SearchFromDB(self.orig_query,
                                fl=self.fl,
                                facet_field=self.facet_field,
                                page=self.page,
                                count=self.count,
                                sort=self.sort,
                                ci_ids=merge_ids,
                                parent_node_perm_passed=parent_node_perm_passed,
                                use_ci_filter=use_ci_filter,
                                only_ids=only_ids).search()

    def _get_ci_filter(self, filter_perms, ci_filters=None):
        ci_filters = ci_filters or []
        if ci_filters:
            result = {}
            for item in ci_filters:
                res = SearchFromDB('_type:{},{}'.format(item['type_id'], item['ci_filter']),
                                   count=sys.maxsize, parent_node_perm_passed=True).get_ci_ids()
                if res:
                    result[item['type_id']] = set(res)

            return {}, result if result else None

        result = dict()
        if filter_perms.get('id_filter'):
            for k in filter_perms['id_filter']:
                node_path = k.split(',')
                if len(node_path) == 1:
                    result[int(node_path[0])] = 1
                elif not self.has_m2m:
                    result.setdefault(node_path[-2], set()).add(int(node_path[-1]))
                else:
                    result.setdefault(','.join(node_path[:-1]), set()).add(int(node_path[-1]))
            if result:
                return result, None
            else:
                return None, None

        return {}, None

    def statistics(self, type_ids, need_filter=True):
        self.level = int(self.level)

        acl = ACLManager('cmdb')

        type2filter_perms = dict()
        if not self.is_app_admin:
            res2 = acl.get_resources(ResourceTypeEnum.CI_FILTER)
            if res2:
                type2filter_perms = CIFilterPermsCRUD().get_by_ids(list(map(int, [i['name'] for i in res2])))

        ids = [self.root_id] if not isinstance(self.root_id, list) else self.root_id
        _tmp, tmp_res = [], []
        level2ids = {}
        for lv in range(1, self.level + 1):
            level2ids[lv] = []

            if need_filter:
                id_filter_limit, ci_filter_limit = None, None
                if len(self.descendant_ids or []) >= lv and type2filter_perms.get(self.descendant_ids[lv - 1]):
                    id_filter_limit, _ = self._get_ci_filter(type2filter_perms[self.descendant_ids[lv - 1]])
                elif type_ids and self.level == lv:
                    ci_filters = [type2filter_perms[type_id] for type_id in type_ids if type_id in type2filter_perms]
                    if ci_filters:
                        id_filter_limit, ci_filter_limit = self._get_ci_filter({}, ci_filters=ci_filters)
                    else:
                        id_filter_limit = {}
                else:
                    id_filter_limit = {}
            else:
                id_filter_limit, ci_filter_limit = {}, {}

            if lv == 1:
                if not self.has_m2m:
                    key, prefix = [str(i) for i in ids], REDIS_PREFIX_CI_RELATION
                else:
                    key = ["{},{}".format(self.ancestor_ids, _id) for _id in ids]
                    if not self.ancestor_ids:
                        key, prefix = [str(i) for i in ids], REDIS_PREFIX_CI_RELATION
                    else:
                        prefix = REDIS_PREFIX_CI_RELATION2

                    level2ids[lv] = [[i] for i in key]

                if not key or id_filter_limit is None:
                    _tmp = [[]] * len(ids)
                    continue

                res = [json.loads(x).items() for x in [i or '{}' for i in rd.get(key, prefix) or []]]
                _tmp = []
                if type_ids and lv == self.level:
                    _tmp = [[i for i in x if i[1] in type_ids and
                             (not id_filter_limit or (key[idx] not in id_filter_limit or
                                                      int(i[0]) in id_filter_limit[key[idx]]) or
                              int(i[0]) in id_filter_limit)] for idx, x in enumerate(res)]
                else:
                    _tmp = [[i for i in x if (not id_filter_limit or (key[idx] not in id_filter_limit or
                                                                      int(i[0]) in id_filter_limit[key[idx]]) or
                                              int(i[0]) in id_filter_limit)] for idx, x in enumerate(res)]

                if ci_filter_limit:
                    _tmp = [[j for j in i if j[1] not in ci_filter_limit or int(j[0]) in ci_filter_limit[j[1]]]
                            for i in _tmp]

            else:

                for idx, item in enumerate(_tmp):
                    if item:
                        if not self.has_m2m:
                            key, prefix = [i[0] for i in item], REDIS_PREFIX_CI_RELATION
                        else:
                            key = list(set(['{},{}'.format(j, i[0]) for i in item for j in level2ids[lv - 1][idx]]))
                            prefix = REDIS_PREFIX_CI_RELATION2

                            level2ids[lv].append(key)

                        if key:
                            res = [json.loads(x).items() for x in [i or '{}' for i in rd.get(key, prefix) or []]]
                            if type_ids and lv == self.level:
                                tmp_res = [[i for i in x if i[1] in type_ids and
                                            (not id_filter_limit or (
                                                    key[idx] not in id_filter_limit or
                                                    int(i[0]) in id_filter_limit[key[idx]]) or
                                             int(i[0]) in id_filter_limit)] for idx, x in enumerate(res)]
                            else:
                                tmp_res = [[i for i in x if (not id_filter_limit or (
                                        key[idx] not in id_filter_limit or
                                        int(i[0]) in id_filter_limit[key[idx]]) or
                                                             int(i[0]) in id_filter_limit)] for idx, x in
                                           enumerate(res)]

                            if ci_filter_limit:
                                tmp_res = [[j for j in i if j[1] not in ci_filter_limit or
                                            int(j[0]) in ci_filter_limit[j[1]]] for i in tmp_res]
                        else:
                            tmp_res = []

                        if tmp_res:
                            _tmp[idx] = [j for i in tmp_res for j in i]
                    else:
                        _tmp[idx] = []
                        level2ids[lv].append([])

        result = {str(_id): len(_tmp[idx]) for idx, _id in enumerate(ids)}

        result.update(
            detail={str(_id): dict(Counter([i[1] for i in _tmp[idx]]).items()) for idx, _id in enumerate(ids)})

        return result

    def search_full(self, type_ids):
        def _get_id2name(_type_id):
            ci_type = CITypeCache.get(_type_id)

            attr = AttributeCache.get(ci_type.unique_id)
            value_table = TableMap(attr=attr).table
            serializer = ValueTypeMap.serialize[attr.value_type]
            unique_value = {i.ci_id: serializer(i.value) for i in value_table.get_by(attr_id=attr.id, to_dict=False)}

            attr = AttributeCache.get(ci_type.show_id) # 疑似问题
            if attr:
                value_table = TableMap(attr=attr).table
                serializer = ValueTypeMap.serialize[attr.value_type]
                show_value = {i.ci_id: serializer(i.value) for i in value_table.get_by(attr_id=attr.id, to_dict=False)}
            else:
                show_value = unique_value

            return show_value, unique_value

        self.level = int(self.level)

        acl = ACLManager('cmdb')

        type2filter_perms = dict()
        if not self.is_app_admin:
            res2 = acl.get_resources(ResourceTypeEnum.CI_FILTER)
            if res2:
                type2filter_perms = CIFilterPermsCRUD().get_by_ids(list(map(int, [i['name'] for i in res2])))

        ids = [self.root_id] if not isinstance(self.root_id, list) else self.root_id

        level_ids = [str(i) for i in ids] # 疑似问题
        result = []
        id2children = {}
        id2name = _get_id2name(type_ids[0])
        for i in level_ids:
            item = dict(id=int(i),
                        type_id=type_ids[0],
                        isLeaf=False,
                        title=id2name[0].get(int(i)),
                        uniqueValue=id2name[1].get(int(i)),
                        children=[])
            result.append(item)
            id2children[str(i)] = item['children']

        for lv in range(1, self.level):
            type_id = type_ids[lv]
            
            if len(type_ids or []) >= lv and type2filter_perms.get(type_id):
                id_filter_limit, _ = self._get_ci_filter(type2filter_perms[type_id])
            else:
                id_filter_limit = {}

            if self.has_m2m and lv != 1:
                key, prefix = [i for i in level_ids], REDIS_PREFIX_CI_RELATION2
            else:
                key, prefix = [i.split(',')[-1] for i in level_ids], REDIS_PREFIX_CI_RELATION
            
            res = [json.loads(x).items() for x in [i or '{}' for i in rd.get(key, prefix) or []]]
            res = [[i for i in x if i[1] == type_id and (not id_filter_limit or (key[idx] not in id_filter_limit or
                                                             int(i[0]) in id_filter_limit[key[idx]]) or
                                     int(i[0]) in id_filter_limit)] for idx, x in enumerate(res)]
            _level_ids = []
            id2name = _get_id2name(type_id)
            for idx, node_path in enumerate(level_ids):
                for child_id, _ in (res[idx] or []):
                    item = dict(id=int(child_id),
                                type_id=type_id,
                                isLeaf=True if lv == self.level - 1 else False,
                                title=id2name[0].get(int(child_id)),
                                uniqueValue=id2name[1].get(int(child_id)),
                                children=[])
                    id2children[node_path].append(item)

                    _node_path = "{},{}".format(node_path, child_id)
                    _level_ids.append(_node_path)
                    id2children[_node_path] = item['children']

            level_ids = _level_ids

        return result

    @staticmethod
    def _get_src_ids(src):
        q = src.get('q') or ''
        if not q.startswith('_type:'):
            q = "_type:{},{}".format(src['type_id'], q)

        return SearchFromDB(q, use_ci_filter=True, only_ids=True, count=100000).search()

    @staticmethod
    def _filter_target_ids(target_ids, type_ids, q):
        if not q.startswith('_type:'):
            q = "_type:({}),{}".format(";".join(map(str, type_ids)), q)

        ci_ids = SearchFromDB(q, ci_ids=target_ids, use_ci_filter=True, only_ids=True, count=100000).search()
        cis = CI.get_by(fl=['id', 'type_id'], only_query=True).filter(CI.id.in_(ci_ids))

        return [(str(i.id), i.type_id) for i in cis]

    @staticmethod
    def _path2level(src_type_id, target_type_ids, path):
        if not src_type_id or not target_type_ids:
            return abort(400, ErrFormat.relation_path_search_src_target_required)

        graph = nx.DiGraph()
        graph.add_edges_from([(n, _path[idx + 1]) for _path in path for idx, n in enumerate(_path[:-1])])
        relation_types = defaultdict(dict)
        level2type = defaultdict(set)
        type2show_key = dict()
        type2multishow_key = dict()
        for _path in path:
            for idx, node in enumerate(_path[1:]):
                level2type[idx + 1].add(node)

                src = CITypeCache.get(_path[idx])
                target = CITypeCache.get(node)
                relation_type = RelationType.get_by(only_query=True).join(
                    CITypeRelation, CITypeRelation.relation_type_id == RelationType.id).filter(
                    CITypeRelation.parent_id == src.id).filter(CITypeRelation.child_id == target.id).first()
                relation_types[src.alias].update({target.alias: relation_type.name})

                if src.id not in type2show_key:
                    type2show_key[src.id] = AttributeCache.get(src.show_id or src.unique_id).name
                if target.id not in type2show_key:
                    type2show_key[target.id] = AttributeCache.get(target.show_id or target.unique_id).name
                    
                if src.id not in type2multishow_key:
                    if src.show_ids:
                        type2multishow_key[src.id] = [AttributeCache.get(i).name for i in src.show_ids]
                        
        nodes = graph.nodes()

        return level2type, list(nodes), relation_types, type2show_key, type2multishow_key

    def _build_graph(self, source_ids, source_type_id, level2type, target_type_ids, acl):
        type2filter_perms = dict()
        if not self.is_app_admin:
            res2 = acl.get_resources(ResourceTypeEnum.CI_FILTER)
            if res2:
                type2filter_perms = CIFilterPermsCRUD().get_by_ids(list(map(int, [i['name'] for i in res2])))

        target_type_ids = set(target_type_ids)
        graph = nx.DiGraph()
        target_ids = []
        
        # 预先构建id_filter_limit字典,避免重复计算
        id_filter_limits = {}
        for level in level2type:
            filter_type_ids = level2type[level]
            id_filter_limit = dict()
            for _type_id in filter_type_ids:
                if type2filter_perms.get(_type_id):
                    _id_filter_limit, _ = self._get_ci_filter(type2filter_perms[_type_id])
                    id_filter_limit.update(_id_filter_limit)
            id_filter_limits[level] = id_filter_limit

        # 批量获取Redis数据
        key = [(str(i), source_type_id) for i in source_ids]
        graph.add_nodes_from(key)

        for level in level2type:
            filter_type_ids = level2type[level]
            id_filter_limit = id_filter_limits[level]
            has_target = filter_type_ids & target_type_ids

            # 批量获取Redis数据
            redis_keys = [i[0] for i in key]
            redis_result = rd.get(redis_keys, REDIS_PREFIX_CI_RELATION)
            
            res = [json.loads(x).items() if x else {} for x in (redis_result or [])]
            _key = []
            
            # 优化内部循环
            for idx, (_id, items) in enumerate(zip(key, res)):
                # 使用列表推导式优化过滤逻辑
                valid_targets = [
                    (i[0], i[1]) for i in items 
                    if i[1] in filter_type_ids and (
                        not id_filter_limit or 
                        int(i[0]) in id_filter_limit.get(_id[0], set()) or
                        int(i[0]) in id_filter_limit
                    )
                ]
                
                if valid_targets:
                    _key.extend(valid_targets)
                    # 批量添加边
                    graph.add_edges_from(zip([_id] * len(valid_targets), valid_targets))

            if has_target:
                # 使用生成器表达式优化内存使用
                target_ids.extend(i[0] for i in _key if i[1] in target_type_ids)

            key = _key

        return graph, target_ids

    @staticmethod
    def _find_paths(graph, source_ids, source_type_id, target_ids, valid_path, max_depth=6, missing_mode=""):
        """
        统一的路径查找方法，支持普通路径查找和缺失关系检测
        :param missing_mode: "", "down", "up" - 缺失关系搜索模式
        """
        paths = []
        target_ids = set(target_ids) if target_ids else set()
        
        if not source_ids:
            current_app.logger.warning("No source_ids provided for path finding")
            return paths
        
        def bfs_paths(source, targets, mode):
            queue = [(source, [source])]
            found_paths = []
            
            while queue:
                (vertex, path) = queue.pop(0)
                path_length = len(path)
                path_types = tuple([i[1] for i in path])
                
                # 根据不同模式调整查找逻辑
                if mode == "down":
                    # 下游缺失：检查当前路径是否缺少下一层关系
                    current_path_length = len(path)
                    max_path_length = max(len(vp) for vp in valid_path.keys())
                    
                    # 对于两级路径（长度为2）
                    if max_path_length == 2:
                        if current_path_length == 1:  # 只有源节点
                            # 检查该源节点是否与任何目标节点有关系
                            has_target_relation = any(
                                next_vertex in targets 
                                for next_vertex in graph.successors(vertex)
                            )
                            
                            if not has_target_relation:
                                found_paths.append([vertex[0]])  # 返回缺失关系的源节点
                            continue
                    
                    # 对于三级及以上路径：查找到倒数第二层的完整路径，但缺少最后一层关系
                    else:
                        target_path_length = max_path_length - 1
                        
                        if current_path_length == target_path_length:
                            # 到达倒数第二层，检查是否是有效路径
                            if path_types in valid_path or any(path_types == vp[:len(path_types)] for vp in valid_path):
                                # 检查是否有指向最后一层的关系
                                has_next_relation = any(
                                    next_vertex in targets 
                                    for next_vertex in graph.successors(vertex)
                                )
                                
                                if not has_next_relation:
                                    found_paths.append([i[0] for i in path])
                            continue
                        
                elif mode == "up":
                    # 上游缺失：从目标开始反向检查
                    if vertex in targets and path_length == 1:
                        # 检查该目标节点是否有有效的上游路径
                        has_valid_upstream = False
                        
                        # 如果目标节点不在图中，说明它没有任何关系，肯定是上游缺失
                        if vertex not in graph.nodes:
                            found_paths.append([vertex[0]])  # 只返回目标节点ID
                            continue
                        
                        # 使用NetworkX检查是否存在路径
                        for source_candidate in [(sid, source_type_id) for sid in source_ids]:
                            # 检查源节点是否在图中，并且是否有路径到目标节点
                            if source_candidate in graph.nodes and nx.has_path(graph, source_candidate, vertex):
                                try:
                                    simple_paths = list(nx.all_simple_paths(
                                        graph, source_candidate, vertex, cutoff=max_depth
                                    ))
                                    
                                    for sp in simple_paths:
                                        sp_types = tuple(node[1] for node in sp)
                                        if sp_types in valid_path:
                                            has_valid_upstream = True
                                            break
                                            
                                except nx.NetworkXNoPath:
                                    continue
                            
                            if has_valid_upstream:
                                break
                        
                        if not has_valid_upstream:
                            found_paths.append([vertex[0]])  # 只返回目标节点ID
                        continue
                        
                else:
                    # 普通模式：现有逻辑
                    if vertex in targets:
                        if tuple([i[1] for i in path]) in valid_path:
                            found_paths.append([i[0] for i in path])
                        continue
                
                # 继续搜索下一层
                if path_length <= max_depth:
                    for next_vertex in graph[vertex]:
                        new_path = path + [next_vertex]
                        queue.append((next_vertex, new_path))
            
            return found_paths

        # 并行处理
        max_workers = max(1, min(len(source_ids), 10))
        
        from concurrent.futures import ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            
            if missing_mode == "up":
                # 上游缺失模式：从目标节点开始
                for target_id, target_type_id in target_ids:
                    target_node = (target_id, target_type_id)
                    futures.append(executor.submit(lambda t: bfs_paths(t, {t}, missing_mode), target_node))
            else:
                # 普通模式和下游缺失模式：从源节点开始
                for source_id in source_ids:
                    source = (source_id, source_type_id)
                    futures.append(executor.submit(lambda s: bfs_paths(s, target_ids, missing_mode), source))
            
            for future in futures:
                paths.extend(future.result())

        return paths

    @staticmethod
    def _wrap_path_result(paths, types, valid_path, target_types, type2show_key, missing="", original_path=None):
        """
        包装路径搜索结果，支持不同的missing模式
        :param missing: "", "down", "up" - 缺失关系搜索模式
        :param original_path: 原始路径，用于missing模式的路径调整
        """
        if not paths:
            return {}, {}, {}
            
        ci_ids = [j for i in paths for j in i]

        response, _, _, _, _, _ = SearchFromDB("_type:({})".format(";".join(map(str, types))),
                                               use_ci_filter=False,
                                               ci_ids=list(map(int, ci_ids)),
                                               count=1000000).search()
        
        # 根据missing模式调整id2ci构建
        if missing in ["down", "up"]:
            # 缺失关系模式：返回完整的CI信息
            id2ci = {str(i.get('_id')): i for i in response}
        else:
            # 普通模式：现有逻辑
            id2ci = {str(i.get('_id')): i if i['_type'] in target_types else {
                type2show_key[i['_type']]: i[type2show_key[i['_type']]],
                "ci_type_alias": i["ci_type_alias"],
                "_type": i["_type"],
            } for i in response}
            id2ci = {str(i.get('_id')): i for i in response}

        # 根据missing模式调整valid_path
        if missing == "down" and original_path:
            # 下游缺失：使用倒数第二层作为验证路径
            adjusted_valid_path = {tuple(p[:-1]): 1 for p in original_path}
        elif missing == "up" and original_path:
            # 上游缺失：只验证目标类型
            target_type_ids = [p[-1] for p in original_path]
            adjusted_valid_path = {tuple([tid]): 1 for tid in target_type_ids}
        else:
            # 普通模式：使用传入的valid_path
            adjusted_valid_path = valid_path

        result = defaultdict(list)
        counter = defaultdict(int)

        for path in paths:
            key = "-".join([id2ci.get(i, {}).get('ci_type_alias') or '' for i in path])
            path_types = tuple([id2ci.get(i, {}).get('_type') for i in path])
            
            if path_types in adjusted_valid_path:
                counter[key] += 1
                result[key].append(path)

        return result, counter, id2ci

    def search_by_path(self, source, target, path, missing=""):
        """
        路径搜索主方法，支持普通搜索和缺失关系搜索

        :param source: {type_id: id, q: expr}
        :param target: {type_ids: [id], q: expr}
        :param path: [source_type_id, ..., target_type_id], use type id
        :param missing: "", "down", "up" - 缺失关系搜索模式
        :return: SearchPathResult namedtuple
        """
        current_app.logger.debug(f"开始执行search_by_path, source={source}, target={target}, path={path}, missing={missing}")
        start_time = time.time()

        acl = ACLManager('cmdb')
        if not self.is_app_admin:
            res = {i['name'] for i in acl.get_resources(ResourceTypeEnum.CI_TYPE)}
            for type_id in (source.get('type_id') and [source['type_id']] or []) + (target.get('type_ids') or []):
                _type = CITypeCache.get(type_id)
                if _type and _type.name not in res:
                    return abort(403, ErrFormat.no_permission.format(_type.alias, PermEnum.READ))

        target['type_ids'] = [i[-1] for i in path]
        level2type, types, relation_types, type2show_key, type2multishow_key = self._path2level(
            source.get('type_id'), target.get('type_ids'), path)
        
        if not level2type:
            return self._create_empty_result(relation_types, type2show_key, type2multishow_key)

        current_app.logger.debug(f"获取path2level耗时: {time.time() - start_time:.3f}秒")
        path_level_time = time.time()

        source_ids = self._get_src_ids(source)
        current_app.logger.debug(f"获取source_ids耗时: {time.time() - path_level_time:.3f}秒, source_ids数量: {len(source_ids)}")
        source_ids_time = time.time()

        if not source_ids:
            current_app.logger.warning(f"No source_ids found for source: {source}")
            return self._create_empty_result(relation_types, type2show_key, type2multishow_key)

        graph, target_ids = self._build_graph(source_ids, source['type_id'], level2type, target['type_ids'], acl)
        current_app.logger.debug(f"构建图耗时: {time.time() - source_ids_time:.3f}秒, 图节点数: {len(graph.nodes)}, 边数: {len(graph.edges)}")
        build_graph_time = time.time()

        # 根据missing模式调整目标过滤策略
        if missing == "up":
            # 上游缺失：需要检查所有可能的目标节点，不仅仅是图中有关系的节点
            # 先获取所有符合条件的目标类型的CI
            all_target_query = target.get('q', '')
            if not all_target_query.startswith('_type:'):
                all_target_query = "_type:({}),{}".format(";".join(map(str, target['type_ids'])), all_target_query)
            
            all_target_ci_ids = SearchFromDB(all_target_query, use_ci_filter=True, only_ids=True, count=100000).search()
            from api.models.cmdb import CI
            all_target_cis = CI.get_by(fl=['id', 'type_id'], only_query=True).filter(CI.id.in_(all_target_ci_ids))
            target_ids = [(str(i.id), i.type_id) for i in all_target_cis]
        elif missing == "down":
            # 下游缺失：需要应用查询条件过滤目标节点，用于缺失关系检测
            target_ids = self._filter_target_ids(target_ids, target['type_ids'], target.get('q') or '')
        else:
            # 普通模式：按现有逻辑过滤
            target_ids = self._filter_target_ids(target_ids, target['type_ids'], target.get('q') or '')
            
        current_app.logger.debug(f"过滤target_ids耗时: {time.time() - build_graph_time:.3f}秒, 过滤后target_ids数量: {len(target_ids)}")
        filter_time = time.time()

        # 调用扩展的路径查找方法
        paths = self._find_paths(graph,
                                 source_ids,
                                 source['type_id'],
                                 set(target_ids) if target_ids else set(),
                                 {tuple(i): 1 for i in path},
                                 missing_mode=missing)

        current_app.logger.debug(f"查找路径耗时: {time.time() - filter_time:.3f}秒, 找到路径数量: {len(paths)}")
        find_paths_time = time.time()

        # 根据missing模式调整types参数
        if missing == "down":
            # 下游缺失：排除最后一层类型
            result_types = types[:-1] if len(types) > 1 else types
            target_types = set(result_types)
        elif missing == "up":
            # 上游缺失：只包含目标类型
            result_types = target['type_ids']
            target_types = set(target['type_ids'])
        else:
            # 普通模式
            result_types = types
            target_types = set(target.get('type_ids') or [])

        numfound = len(paths)
        paginated_paths = paths[(self.page - 1) * self.count:self.page * self.count]
        
        response, counter, id2ci = self._wrap_path_result(paginated_paths,
                                                          result_types,
                                                          {tuple(i): 1 for i in path},
                                                          target_types,
                                                          type2show_key,
                                                          missing=missing,
                                                          original_path=path)

        current_app.logger.debug(f"包装结果耗时: {time.time() - find_paths_time:.3f}秒")
        current_app.logger.debug(f"search_by_path总耗时: {time.time() - start_time:.3f}秒")

        return SearchPathResult(
            response=response,
            counter=counter,
            total=len(paginated_paths),
            page=self.page,
            numfound=numfound,
            id2ci=id2ci,
            relation_types=relation_types,
            type2show_key=type2show_key,
            type2multishow_key=type2multishow_key
        )

    def _create_empty_result(self, relation_types=None, type2show_key=None, type2multishow_key=None):
        """创建空的搜索结果"""
        return SearchPathResult(
            response={},
            counter={},
            total=0,
            page=self.page,
            numfound=0,
            id2ci={},
            relation_types=relation_types or {},
            type2show_key=type2show_key or {},
            type2multishow_key=type2multishow_key or {}
        )
