<template>
  <div
    ref="relationSearchRef"
    class="relation-search"
    :style="{ height: `${windowHeight - 131}px` }"
  >
    <div class="relation-search-wrap">
      <div
        v-if="!isSearch"
        class="relation-search-title"
      >
        <ops-icon class="relation-search-title-icon" type="veops-relationship2" />
        <div class="relation-search-title-text">{{ $t('cmdb.relationSearch.relationSearch') }}</div>
      </div>

      <div
        v-if="isHideSearchCondition"
        class="relation-search-expand"
      >
        <div class="relation-search-expand-line"></div>

        <div class="relation-search-expand-right">
          <div
            class="relation-search-expand-handle"
            @click="isHideSearchCondition = false"
          >
            <a-icon
              type="down"
              class="relation-search-expand-icon"
            />
          </div>
          <div
            class="relation-search-expand-text"
            @click="isHideSearchCondition = false"
          >
            {{ $t('cmdb.relationSearch.expandCondition') }}
          </div>
        </div>
      </div>

      <SearchCondition
        v-else
        :CITypeGroup="CITypeGroup"
        :sourceCIType="sourceCIType"
        :sourceCITypeSearchValue="sourceCITypeSearchValue"
        :sourceAllAttributesList="sourceAllAttributesList"
        :sourceExpression="sourceExpression"
        :targetCITypes="targetCITypes"
        :targetCITypeGroup="targetCITypeGroup"
        :targetAllAttributesList="targetAllAttributesList"
        :targetExpression="targetExpression"
        :returnPath="returnPath"
        :missingSearch="missingSearch"
        :allPath="allPath"
        :selectedPath="selectedPath"
        :isSearch="isSearch"
        :isSearchLoading="isSearchLoading"
        @changeData="changeData"
        @search="handleSearch"
        @hideSearchCondition="isHideSearchCondition = true"
        @clickFavor="clickFavor"
      />

      <div
        v-if="isSearch"
        class="relation-search-main"
      >
        <CITable
          :allTableData="allTableData"
          :tabActive="tableTabActive"
          :returnPath="returnPath"
          :isHideSearchCondition="isHideSearchCondition"
          :referenceShowAttrNameMap="referenceShowAttrNameMap"
          :referenceCIIdMap="referenceCIIdMap"
          :searchValue="sourceCITypeSearchValue"
          :isSearchLoading="isSearchLoading"
          :totalNumber="totalNumber"
          @updateTab="(tab) => tableTabActive = tab"
        />

        <div class="relation-search-pagination">
          <a-pagination
            :showSizeChanger="true"
            :current="page"
            size="small"
            :total="totalNumber"
            show-quick-jumper
            :page-size="pageSize"
            :page-size-options="pageSizeOptions"
            :show-total="
              (total, range) =>
                $t('pagination.total', {
                  range0: range[0],
                  range1: range[1],
                  total,
                })
            "
            @showSizeChange="handlePageSizeChange"
            @change="changePage"
          >
            <template slot="buildOptionText" slot-scope="props">
              <span v-if="props.value !== '100000'">{{ props.value }}{{ $t('itemsPerPage') }}</span>
              <span v-if="props.value === '100000'">{{ $t('all') }}</span>
            </template>
          </a-pagination>
        </div>
      </div>
    </div>

    <img
      v-if="!isSearch"
      class="relation-search-bg"
      :src="require('@/modules/cmdb/assets/resourceSearch/resource_search_bg_1.png')"
    />
  </div>
</template>

<script>
import _ from 'lodash'

import { getCITypeAttributesByTypeIds } from '@/modules/cmdb/api/CITypeAttr'
import { getRecursive_level2children, getCITypeRelationPath } from '@/modules/cmdb/api/CITypeRelation'
import { searchCIRelationPath } from '@/modules/cmdb/api/CIRelation'
import { getCITypes } from '@/modules/cmdb/api/CIType'
import { getSubscribeAttributes } from '@/modules/cmdb/api/preference'
import { searchCI } from '@/modules/cmdb/api/ci'
import { strLength } from '@/modules/cmdb/utils/helper.js'

import SearchCondition from './components/searchCondition.vue'
import CITable from './components/ciTable.vue'

export default {
  name: 'RelationSearch',
  components: {
    SearchCondition,
    CITable
  },
  props: {
    CITypeGroup: {
      type: Array,
      default: () => []
    },
    allCITypes: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isSearch: false, // 是否搜索
      isHideSearchCondition: false, // 是否隐藏搜索条件
      isWatchData: true, // 是否监听数据变化
      isSearchLoading: false, // 搜索中

      sourceCIType: undefined, // 已选源模型
      sourceCITypeSearchValue: '', // 源模型搜索关键词
      sourceAllAttributesList: [], // 源模型所有属性
      sourceExpression: '', // 源模型表达式

      targetCITypes: [], // 目标模型
      targetCITypeGroup: {}, // 目标模型分组
      targetAllAttributesList: [], // 目标模型所有属性
      targetExpression: '', // 目标模型表达式

      returnPath: true, // 表格是否展示路径详情
      missingSearch: '', // 缺失关系搜索模式：'up'=上游缺失，'down'=下游缺失，''=正常搜索
      allPath: [], // 所有路径选项
      selectedPath: [], // 已选择路径

      // table
      page: 1,
      pageSize: 50,
      pageSizeOptions: ['50', '100', '200', '100000'],
      allTableData: {}, // 表格数据
      totalNumber: 0, // 数据总数
      tableTabActive: '', // 当前 table tab
      referenceShowAttrNameMap: {},
      referenceCIIdMap: {},
    }
  },
  computed: {
    windowHeight() {
      return this.$store.state.windowHeight
    },
    watchParams() {
      return {
        sourceCIType: this.sourceCIType,
        targetCITypes: this.targetCITypes
      }
    },
  },
  watch: {
    sourceCIType: {
      immediate: true,
      deep: true,
      handler(id) {
        if (this.isWatchData) {
          this.sourceExpression = ''

          this.targetCITypes = []
          this.targetAllAttributesList = []
          this.targetExpression = ''

          this.selectedPath = []

          this.getTargetCITypeGroup(id)
          this.updateSourceAllAttributesList(id)
        }
      }
    },
    targetCITypes: {
      immediate: true,
      deep: true,
      handler(ids) {
        if (this.isWatchData) {
          this.selectedPath = []
          this.targetExpression = ''
          this.updateTargetAllAttributesList(ids)
        }
      }
    },
    watchParams: {
      immediate: true,
      deep: true,
      handler(data) {
        if (this.isWatchData) {
          this.updateAllPath(data)
        }
      }
    }
  },
  methods: {
    changeData(data) {
      this[data.name] = data.value
    },

    async updateSourceAllAttributesList(id) {
      if (id) {
        const res = await getCITypeAttributesByTypeIds({ type_ids: id })
        this.sourceAllAttributesList = res.attributes
      } else {
        this.sourceAllAttributesList = []
      }
    },

    async getTargetCITypeGroup(id) {
      let targetCITypeGroup = {}
      if (id) {
        const res = await getRecursive_level2children(id)
        targetCITypeGroup = res
      }
      this.targetCITypeGroup = targetCITypeGroup
    },

    async updateTargetAllAttributesList(ids) {
      if (ids?.length) {
        const res = await getCITypeAttributesByTypeIds({ type_ids: ids.join(',') })
        this.targetAllAttributesList = res.attributes
      } else {
        this.targetAllAttributesList = []
      }
    },

    async updateAllPath(data) {
      let allPath = []
      if (
        data.sourceCIType &&
        data?.targetCITypes?.length
      ) {
        const params = {
          source_type_id: data.sourceCIType,
          target_type_ids: data.targetCITypes.join(',')
        }

        const res = await getCITypeRelationPath(params)

        if (res?.paths?.length) {
          // 只保留最长的路径
          const maxLength = Math.max(...res.paths.map(path => path.length))
          const longestPaths = res.paths.filter(path => path.length === maxLength)

          const sourceCIType = this.allCITypes.find((ciType) => ciType.id === data.sourceCIType)
          const sourceCITypeName = sourceCIType?.alias || sourceCIType?.name || ''
          const targetCITypeList = Object.values(this.targetCITypeGroup).reduce((acc, cur) => acc.concat(cur), [])
          // res.paths 单位-系统-CVM
          allPath = longestPaths.map((ids) => {
            const [sourceId, ...targetIds] = ids
            const pathNames = [sourceCITypeName]

            targetIds.forEach((id) => {
              const ciType = targetCITypeList.find((item) => item.id === id)
              if (ciType) {
                pathNames.push(ciType.alias || ciType.name)
              }
            })
            // 获取路径

            return {
              value: ids.join(','),
              sourceId,
              targetIds,
              pathNames: pathNames.join('-'),
            }
          })
        }
      }

      this.allPath = allPath
    },

    async loadCI() {
      this.isSearchLoading = true

      const path = this.selectedPath.map((item) => {
        return item?.split(',')?.map((id) => Number(id)) || []
      })

      const params = {
        page: this.page,
        page_size: this.pageSize,
        source: {
          type_id: this.sourceCIType
        },
        target: {
          type_ids: this.targetCITypes
        },
        path
      }

      // 添加缺失关系搜索参数
      if (this.missingSearch) {
        params.missing = this.missingSearch
      }

      const regQ = /(?<=q=).+(?=&)|(?<=q=).+$/g
      const sourceExp = this.sourceExpression.match(regQ) ? this.sourceExpression.match(regQ)[0] : null
      const targetExp = this.targetExpression.match(regQ) ? this.targetExpression.match(regQ)[0] : null
      const sourceSearch = `${sourceExp ? `${sourceExp}` : ''}${this.sourceCITypeSearchValue ? `,*${this.sourceCITypeSearchValue}*` : ''}`

      if (sourceSearch) {
        params.source.q = sourceSearch
      }
      if (targetExp) {
        params.target.q = targetExp
      }

      let res = {}
      const tableData = {}
      const typeId2Attr = {}
      let pathKeyList = []

      try {
        res = await searchCIRelationPath(params)
        pathKeyList = Object.keys(res.paths)
        let filterAllPath = this.allPath.filter((path) => pathKeyList.includes(path.pathNames))

        // 对于缺失关系搜索，如果直接匹配不到，尝试部分匹配
        if (filterAllPath.length === 0 && this.missingSearch) {
          filterAllPath = this.allPath.filter((path) => {
            const pathParts = path.pathNames.split('-')
            return pathKeyList.some(key => pathParts.includes(key))
          })
        }
        // 获取所有涉及的类型ID - 既包括路径中的，也包括实际返回数据中的
        let typeIds = []

        // 从路径配置中获取类型ID
        if (filterAllPath.length > 0) {
          typeIds = _.uniq(
            filterAllPath.map((item) => item?.value.split(',').map(Number))
          ).flat()
        }

        // 从实际返回的CI数据中获取类型ID
        const actualTypeIds = []
        Object.values(res.paths || {}).forEach(pathList => {
          pathList.forEach(ids => {
            ids.forEach(id => {
              const ci = res?.id2ci?.[id]
              if (ci && ci._type) {
                actualTypeIds.push(ci._type)
              }
            })
          })
        })

        // 合并两个来源的类型ID
        typeIds = _.uniq([...typeIds, ...actualTypeIds])
        const promises = typeIds.map((id) => {
          return getSubscribeAttributes(id)
        })
        const subscribedRes = await Promise.all(promises)
        typeIds.forEach((id, index) => {
          const attrList = subscribedRes?.[index]?.attributes || []
          typeId2Attr[id] = attrList
        })
      } catch (error) {
        this.isSearchLoading = false
        this.allTableData = {}
        this.totalNumber = 0
        this.tableTabActive = ''
        return
      }

      pathKeyList.forEach((key) => {
        // 对于缺失关系搜索，返回的key可能是简化的（如"单位"），而allPath中存储的是完整路径（如"系统-单位"）
        // 需要智能匹配：优先精确匹配，如果找不到则进行部分匹配
        let pathObj = this.allPath.find((path) => path.pathNames === key)

        if (!pathObj && this.missingSearch) {
          // 缺失关系搜索模式下，尝试找到包含该key的路径
          pathObj = this.allPath.find((path) => {
            const pathParts = path.pathNames.split('-')
            return pathParts.includes(key)
          })
        }

        // 获取实际返回数据的第一个路径，用于确定实际有多少CI
        const firstActualPath = res?.paths?.[key]?.[0] || []

        const pathIdList = pathObj?.value?.split(',') || []
        const pathNameList = key?.split('-') || []
        const pathList = []

        // 根据实际返回的数据长度来构建pathList，而不是根据预期的完整路径
        const actualDataLength = firstActualPath.length
        const expectedLength = pathNameList.length
                // 为实际返回的每个CI位置创建列
        for (let index = 0; index < actualDataLength; index++) {
          let name, typeId

          if (index < pathNameList.length) {
            // 使用预期的名称
            name = pathNameList[index]

            // 对于缺失关系搜索，需要从实际返回的CI数据中获取类型ID
            if (this.missingSearch && firstActualPath.length > 0) {
              const actualCiId = firstActualPath[index]
              const actualCi = res?.id2ci?.[actualCiId]
              typeId = actualCi?._type || (pathIdList[index] ? Number(pathIdList[index]) : null)
            } else {
              // 正常搜索，使用路径中的类型ID
              typeId = pathIdList[index] ? Number(pathIdList[index]) : null
            }
          } else {
            // 如果实际数据比预期路径长，使用默认名称
            name = `CI${index + 1}`
            // 从实际CI数据中获取类型ID
            if (firstActualPath.length > index) {
              const actualCiId = firstActualPath[index]
              const actualCi = res?.id2ci?.[actualCiId]
              typeId = actualCi?._type || null
            } else {
              typeId = null
            }
          }

          let relation = ''
          if (index < expectedLength - 1 && index < actualDataLength - 1) {
            const targetName = pathNameList[index + 1]
            const sourceRelation = res?.relation_types?.[name]
            if (sourceRelation && Object.keys(sourceRelation)?.includes?.(targetName)) {
              relation = sourceRelation?.[targetName] || ''
            }
          }

          if (typeId) {
            const multiShowKeys = res?.type2multishow_key?.[typeId] || []
            if (multiShowKeys.length) {
              // 如果有多字段显示，为每个字段创建一个路径列
              multiShowKeys.forEach(key => {
                pathList.push({
                  id: `${typeId}_${key}`,
                  typeId,
                  name: `${name}(${typeId2Attr[typeId]?.find(attr => attr.name === key)?.alias || key})`,
                  relation,
                  fieldName: key,
                  isMultiField: true
                })
              })
            } else {
              // 否则使用默认显示字段
              pathList.push({
                id: String(typeId),
                typeId,
                name,
                relation,
                fieldName: res?.type2show_key?.[typeId],
                isMultiField: false
              })
            }
          }
        }

        tableData[key] = {
          key,
          count: res.paths?.[key]?.length || 0,
          pathList,
          ciAttr: [],
          multiShowAttrs: {},
          ciList: []
        }
        if (pathObj) {
          // 选择最后一个CI作为详情展示的CI（这是表格右侧属性列展示的CI）
          const firstIds = res?.paths?.[key]?.[0] || []
          const targetId = firstIds[firstIds.length - 1] // 总是选择路径中的最后一个CI

          const ciTypeId = (res?.id2ci?.[targetId] || {})?._type
          const multiShowKeys = res?.type2multishow_key?.[ciTypeId] || []
          const defaultShowKey = res?.type2show_key?.[ciTypeId]

          if (ciTypeId && typeId2Attr[ciTypeId]) {
            if (multiShowKeys.length) {
              tableData[key].ciAttr = multiShowKeys.map(key => {
                return typeId2Attr[ciTypeId].find(attr => attr.name === key)
              }).filter(Boolean)
            } else if (defaultShowKey) {
              const defaultAttr = typeId2Attr[ciTypeId].find(attr => attr.name === defaultShowKey)
              if (defaultAttr) {
                tableData[key].ciAttr = [defaultAttr]
              }
            }

            // 添加这部分代码来保留原有属性
            const subscribedAttrs = typeId2Attr[ciTypeId] || []
            if (subscribedAttrs.length) {
              tableData[key].ciAttr = tableData[key].ciAttr || []
              tableData[key].ciAttr.push(...subscribedAttrs)
              // 去重
              tableData[key].ciAttr = _.uniqBy(tableData[key].ciAttr, 'name')
            }
          }

          tableData[key].ciList = res.paths[key].map((ids) => {
            const pathCI = {}
            ids.forEach((id) => {
              const ci = res?.id2ci?.[id] || {}
              const typeId = ci._type
              const multiShowKeys = res?.type2multishow_key?.[typeId] || []
              const defaultShowKey = res?.type2show_key?.[typeId]

              if (multiShowKeys.length) {
                // 修改：为每个多字段创建独立的字段值
                multiShowKeys.forEach(key => {
                  pathCI[`${typeId}_${key}`] = ci[key]
                })
              } else {
                // 使用默认字段
                pathCI[String(typeId)] = ci[defaultShowKey] ?? ''
              }
            })
            // 选择最后一个CI作为targetCI（用于右侧属性列的展示）
            const targetId = ids[ids.length - 1]
            const targetCI = res?.id2ci?.[targetId] || {}

            return {
              pathCI, // 现在的 pathCI 包含了所有多字段的独立值
              targetCI
            }
          })

          let totalWidth = 0
          tableData[key].ciAttr.forEach((attr) => {
            const lengthList = tableData[key].ciList.map(({ targetCI }) => {
              return strLength(targetCI[attr.name])
            })

            attr.width = Math.round(Math.min(Math.max(100, ...lengthList), 350))
            totalWidth += attr.width
          })

          const wrapWidth = this.$refs?.relationSearchRef?.clientWidth - (tableData?.[key]?.pathList.length || 0) * 160 - 60

          if (wrapWidth && totalWidth < wrapWidth) {
            tableData[key].ciAttr.forEach((attr) => {
              delete attr.width
            })
          }
        }
      })
      this.$set(this, 'allTableData', tableData)
      this.allTableData = tableData
      this.totalNumber = res?.numfound ?? 0
      this.tableTabActive = Object.keys(tableData)?.[0] || ''
      this.isSearch = true
      this.isSearchLoading = false

      const allAttr = []
      Object.values(typeId2Attr).map((attrList) => {
        allAttr.push(...attrList)
      })
      this.handlePerference(_.uniqBy(allAttr, 'id'))
    },

    handlePerference(allAttr) {
      let needRequiredCIType = []
      allAttr.forEach((attr) => {
        if (attr?.is_reference && attr?.reference_type_id) {
          needRequiredCIType.push(attr)
        }
      })
      needRequiredCIType = _.uniq(needRequiredCIType, 'id')

      if (!needRequiredCIType.length) {
        this.referenceShowAttrNameMap = {}
        this.referenceCIIdMap = {}
        return
      }

      this.handleReferenceShowAttrName(needRequiredCIType)
      this.handleReferenceCIIdMap(needRequiredCIType)
    },

    async handleReferenceShowAttrName(needRequiredCIType) {
      const res = await getCITypes({
        type_ids: needRequiredCIType.map((col) => col.reference_type_id).join(',')
      })

      const map = {}
      res.ci_types.forEach((ciType) => {
        map[ciType.id] = ciType?.show_name || ciType?.unique_name || ''
      })

      this.referenceShowAttrNameMap = map
    },

    async handleReferenceCIIdMap(needRequiredCIType) {
      const map = {}

      Object.values(this.allTableData).forEach((item) => {
        const ciList = item?.ciList || []
        ciList.forEach(({ targetCI }) => {
          needRequiredCIType.forEach((col) => {
            const ids = Array.isArray(targetCI[col.name]) ? targetCI[col.name] : targetCI[col.name] ? [targetCI[col.name]] : []
            if (ids.length) {
              if (!map?.[col.reference_type_id]) {
                map[col.reference_type_id] = {}
              }
              ids.forEach((id) => {
                map[col.reference_type_id][id] = {}
              })
            }
          })
        })
      })

      if (!Object.keys(map).length) {
        this.referenceCIIdMap = {}
        return
      }

      const allRes = await Promise.all(
        Object.keys(map).map((key) => {
          return searchCI({
            q: `_type:${key},_id:(${Object.keys(map[key]).join(';')})`,
            count: 9999
          })
        })
      )

      allRes.forEach((res) => {
        res.result.forEach((item) => {
          if (map?.[item._type]?.[item._id]) {
            map[item._type][item._id] = item
          }
        })
      })

      this.referenceCIIdMap = map
    },

    handlePageSizeChange(_, pageSize) {
      this.pageSize = pageSize
      this.page = 1
      this.loadCI()
    },

    changePage(page) {
      this.page = page
      this.loadCI()
    },

    handleSearch() {
      this.page = 1
      this.loadCI()
    },

    clickFavor(option) {
      this.isWatchData = false

      this.$nextTick(async () => {
        this.sourceCIType = option?.sourceCIType || undefined
        this.sourceCITypeSearchValue = option?.searchValue || ''
        this.sourceExpression = option?.sourceExpression || ''
        this.targetCITypes = option?.targetCITypes || []
        this.targetExpression = option?.targetExpression || ''
        this.selectedPath = option?.selectedPath || []
        this.missingSearch = option?.missingSearch || ''

        await Promise.all([
          this.getTargetCITypeGroup(this.sourceCIType),
          this.updateSourceAllAttributesList(this.sourceCIType),
          this.updateTargetAllAttributesList(this.targetCITypes)
        ])
        await this.updateAllPath({
          sourceCIType: this.sourceCIType,
          targetCITypes: this.targetCITypes
        })

        this.isWatchData = true
        this.page = 1

        this.loadCI()
      })
    },

  }
}
</script>

<style lang="less" scoped>
.relation-search {
  width: 100%;
  height: 100%;
  position: relative;

  &-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  &-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    margin-top: 100px;

    &-icon {
      font-size: 28px;
      margin-right: 10px;
    }

    &-text {
      font-size: 20px;
      font-weight: 700;
      color: #1D2129;
    }
  }

  &-expand {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;

    &-line {
      width: 650px;
      height: 1px;
      background-color: #E4E7ED;
    }

    &-icon {
      font-size: 12px;
      color: #86909C;
    }

    &-text {
      margin-left: 5px;
      font-size: 12px;
      font-weight: 400;
      color: #A5A9BC;
    }

    &-handle {
      width: 14px;
      height: 14px;
      background-color: #EBEFF8;
      border-radius: 1px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &-right {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      cursor: pointer;

      &:hover {
        .relation-search-expand-handle {
          background-color: #D9E4FA;
        }

        .relation-search-expand-icon {
          color: #2F54EB;
        }

        .relation-search-expand-text {
          color: #2F54EB;
        }
      }
    }
  }

  &-bg {
    position: absolute;
    left: -24px;
    bottom: -24px;
    width: calc(100% + 48px);
    z-index: 0;
  }

  &-main {
    width: calc(100% + 48px);
    // height: 100%;
    background-color: #FFFFFF;
    padding: 24px;
  }

  &-pagination {
    text-align: right;
    margin-top: 12px;
  }
}
</style>
