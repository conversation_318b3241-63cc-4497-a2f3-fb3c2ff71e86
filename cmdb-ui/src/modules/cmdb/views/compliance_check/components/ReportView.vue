<template>
  <div class="report-view">
    <!-- 报告头部 -->
    <div class="report-header">
      <h3>{{ report.taskName }}</h3>
      <div class="report-meta">
        <span>执行时间：{{ report.executeTime }}</span>
        <span>耗时：{{ formatDuration(report.duration) }}</span>
        <span>报告ID：{{ report.reportId }}</span>
      </div>
    </div>

    <!-- 违规摘要 -->
    <a-row :gutter="16" class="report-summary">
      <a-col :span="6">
        <a-statistic :title="$t('cmdb.complianceCheck.totalViolations')" :value="report.summary.totalViolations" />
      </a-col>
      <a-col :span="6">
        <a-statistic :title="$t('cmdb.complianceCheck.highRisk')" :value="report.summary.severityBreakdown.high" />
      </a-col>
      <a-col :span="6">
        <a-statistic :title="$t('cmdb.complianceCheck.mediumRisk')" :value="report.summary.severityBreakdown.medium" />
      </a-col>
      <a-col :span="6">
        <a-statistic :title="$t('cmdb.complianceCheck.lowRisk')" :value="report.summary.severityBreakdown.low" />
      </a-col>
    </a-row>

    <!-- 违规详情 -->
    <div class="report-details">
      <h4>{{ $t('cmdb.complianceCheck.violationDetails') }}</h4>
      <div
        v-for="(result, index) in report.results"
        :key="index"
        class="search-result-section"
      >
        <div class="section-header">
          <h5>{{ $t('cmdb.complianceCheck.searchCondition') }} {{ index + 1 }}（{{ getSearchTypeText(result.searchType) }}）</h5>
          <a-tag color="blue">违规项数: {{ result.violationCount }}</a-tag>
        </div>

        <!-- 搜索配置展示 -->
        <div class="search-config">
          <SearchConfigDisplay :config="result.searchConfig" :type="result.searchType" />
        </div>

        <!-- 违规项表格 -->
        <a-table
          :columns="violationColumns"
          :dataSource="result.violations"
          :pagination="{ pageSize: 10, showSizeChanger: true }"
          size="small"
          rowKey="ciId"
        >
          <template slot="severity" slot-scope="severity">
            <a-tag :color="getSeverityColor(severity)">
              {{ getSeverityText(severity) }}
            </a-tag>
          </template>

          <template slot="actions" slot-scope="text, record">
            <a-button size="small" @click="viewCIDetail(record.ciId)">
              {{ $t('cmdb.complianceCheck.viewDetail') }}
            </a-button>
          </template>
        </a-table>
      </div>

      <!-- 无违规项提示 -->
      <div v-if="report.summary.totalViolations === 0" class="no-violations">
        <a-empty :description="$t('cmdb.complianceCheck.noViolationsFound')" />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="report-actions">
      <a-button type="primary" @click="exportPDF">
        {{ $t('cmdb.complianceCheck.exportPDF') }}
      </a-button>
      <a-button @click="exportExcel">
        {{ $t('cmdb.complianceCheck.exportExcel') }}
      </a-button>
      <a-button @click="sendEmail">
        {{ $t('cmdb.complianceCheck.sendEmail') }}
      </a-button>
    </div>

    <!-- 发送邮件弹窗 -->
    <a-modal
      title="发送邮件报告"
      :visible="emailModalVisible"
      @ok="handleSendEmail"
      @cancel="handleEmailCancel"
    >
      <a-form-model :model="emailForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-model-item label="收件人">
          <a-select
            v-model="emailForm.emails"
            mode="tags"
            style="width: 100%"
            placeholder="请输入邮箱地址，支持多个"
          />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { sendEmailReport } from '@/modules/cmdb/api/complianceCheck'
import { exportPDFReport, exportExcelReport } from '../utils/reportGenerator'
import { formatDuration, getSeverityColor, getSeverityText, getSearchTypeText } from '../utils/scheduleUtils'
import SearchConfigDisplay from './SearchConfigDisplay.vue'

export default {
  name: 'ReportView',
  components: {
    SearchConfigDisplay
  },
  props: {
    report: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      emailModalVisible: false,
      emailForm: {
        emails: []
      },
      violationColumns: [
        {
          title: this.$t('cmdb.complianceCheck.ciId'),
          dataIndex: 'ciId',
          key: 'ciId',
          width: 120
        },
        {
          title: this.$t('cmdb.complianceCheck.ciType'),
          dataIndex: 'ciType',
          key: 'ciType',
          width: 120
        },
        {
          title: this.$t('cmdb.complianceCheck.ciName'),
          dataIndex: 'ciName',
          key: 'ciName',
          width: 150
        },
        {
          title: this.$t('cmdb.complianceCheck.violationReason'),
          dataIndex: 'violationReason',
          key: 'violationReason',
          ellipsis: true
        },
        {
          title: this.$t('cmdb.complianceCheck.severity'),
          dataIndex: 'severity',
          key: 'severity',
          width: 100,
          scopedSlots: { customRender: 'severity' }
        },
        {
          title: this.$t('cmdb.complianceCheck.actions'),
          key: 'actions',
          width: 100,
          scopedSlots: { customRender: 'actions' }
        }
      ]
    }
  },
  methods: {
    formatDuration,
    getSeverityColor,
    getSeverityText,
    getSearchTypeText,

    // 导出PDF
    exportPDF() {
      try {
        exportPDFReport(this.report)
        this.$message.success('PDF导出成功')
      } catch (error) {
        console.error('PDF导出失败:', error)
        this.$message.error('PDF导出失败')
      }
    },

    // 导出Excel
    exportExcel() {
      try {
        exportExcelReport(this.report, `合规检查报告_${this.report.reportId}`)
        this.$message.success('Excel导出成功')
      } catch (error) {
        console.error('Excel导出失败:', error)
        this.$message.error('Excel导出失败')
      }
    },

    // 发送邮件
    sendEmail() {
      this.emailForm.emails = []
      this.emailModalVisible = true
    },

    // 处理发送邮件
    async handleSendEmail() {
      if (!this.emailForm.emails || this.emailForm.emails.length === 0) {
        this.$message.warning('请输入收件人邮箱')
        return
      }

      try {
        await sendEmailReport(this.report.executionId, this.emailForm.emails)
        this.$message.success(this.$t('cmdb.complianceCheck.emailSentSuccess'))
        this.emailModalVisible = false
      } catch (error) {
        console.error('发送邮件失败:', error)
        this.$message.error('发送邮件失败')
      }
    },

    // 取消发送邮件
    handleEmailCancel() {
      this.emailModalVisible = false
    },

    // 查看CI详情
    viewCIDetail(ciId) {
      // 这里应该打开CI详情页面或抽屉
      console.log('查看CI详情:', ciId)
      this.$message.info(`查看CI详情: ${ciId}`)
    }
  }
}
</script>

<style lang="less" scoped>
.report-view {
  .report-header {
    text-align: center;
    margin-bottom: 24px;

    h3 {
      margin-bottom: 8px;
      font-size: 18px;
      font-weight: 500;
    }

    .report-meta {
      color: #666;

      span {
        margin: 0 16px;
      }
    }
  }

  .report-summary {
    margin-bottom: 32px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;
  }

  .report-details {
    margin-bottom: 32px;

    h4 {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;
    }

    .search-result-section {
      margin-bottom: 32px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 16px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h5 {
          margin: 0;
          font-size: 14px;
          font-weight: 500;
        }
      }

      .search-config {
        margin-bottom: 16px;
        padding: 12px;
        background-color: #fafafa;
        border-radius: 4px;
      }
    }

    .no-violations {
      text-align: center;
      padding: 40px;
    }
  }

  .report-actions {
    text-align: center;
    padding: 16px 0;
    border-top: 1px solid #e8e8e8;

    .ant-btn {
      margin: 0 8px;
    }
  }
}
</style>
