<template>
  <div class="task-monitor">
    <div class="monitor-header">
      <h3>{{ $t('cmdb.complianceCheck.realTimeMonitor') }}</h3>
      <a-button @click="refreshStatus" :loading="loading">
        <a-icon type="reload" />
        刷新状态
      </a-button>
    </div>

    <div class="monitor-content">
      <!-- 执行状态 -->
      <a-card title="执行状态" class="status-card">
        <div class="status-info">
          <div class="status-item">
            <span class="status-label">当前状态:</span>
            <a-badge :status="getStatusBadge(taskStatus.status)" :text="getStatusText(taskStatus.status)" />
          </div>
          <div class="status-item">
            <span class="status-label">开始时间:</span>
            <span>{{ taskStatus.startTime || '-' }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">运行时长:</span>
            <span>{{ getRunningDuration() }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">当前进度:</span>
            <a-progress
              :percent="taskStatus.progress || 0"
              :status="taskStatus.status === 'failed' ? 'exception' : 'active'"
            />
          </div>
        </div>
      </a-card>

      <!-- 执行日志 -->
      <a-card title="执行日志" class="log-card">
        <div class="log-container">
          <div
            v-for="(log, index) in executionLogs"
            :key="index"
            :class="['log-item', `log-${log.level}`]"
          >
            <span class="log-time">{{ log.timestamp }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="executionLogs.length === 0" class="log-empty">
            暂无执行日志
          </div>
        </div>
      </a-card>

      <!-- 实时统计 -->
      <a-card title="实时统计" class="stats-card">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="已检查条件" :value="taskStatus.checkedConditions || 0" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="总条件数" :value="taskStatus.totalConditions || 0" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="发现违规" :value="taskStatus.violationsFound || 0" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="处理速度" :value="taskStatus.processingSpeed || 0" suffix="条/秒" />
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { getTaskExecutionStatus } from '@/modules/cmdb/api/complianceCheck'
import { getStatusBadge, getStatusText } from '../utils/scheduleUtils'

export default {
  name: 'TaskMonitor',
  props: {
    taskId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      taskStatus: {
        status: 'running',
        startTime: null,
        progress: 0,
        checkedConditions: 0,
        totalConditions: 0,
        violationsFound: 0,
        processingSpeed: 0
      },
      executionLogs: [],
      refreshTimer: null
    }
  },
  mounted() {
    this.startMonitoring()
  },
  beforeDestroy() {
    this.stopMonitoring()
  },
  methods: {
    getStatusBadge,
    getStatusText,

    // 开始监控
    startMonitoring() {
      this.refreshStatus()
      // 每5秒刷新一次状态
      this.refreshTimer = setInterval(() => {
        this.refreshStatus()
      }, 5000)
    },

    // 停止监控
    stopMonitoring() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    // 刷新状态
    async refreshStatus() {
      this.loading = true
      try {
        const response = await getTaskExecutionStatus(this.taskId)
        this.taskStatus = response.status || {}
        this.executionLogs = response.logs || []

        // 如果任务已完成，停止监控
        if (this.taskStatus.status === 'success' || this.taskStatus.status === 'failed') {
          this.stopMonitoring()
        }
      } catch (error) {
        console.error('获取任务状态失败:', error)
        // 模拟数据用于演示
        this.mockTaskStatus()
      } finally {
        this.loading = false
      }
    },

    // 模拟任务状态（用于演示）
    mockTaskStatus() {
      const now = moment()
      const startTime = moment().subtract(2, 'minutes')

      this.taskStatus = {
        status: 'running',
        startTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
        progress: Math.min(85, Math.floor(Math.random() * 100)),
        checkedConditions: Math.floor(Math.random() * 50) + 20,
        totalConditions: 60,
        violationsFound: Math.floor(Math.random() * 10),
        processingSpeed: Math.floor(Math.random() * 5) + 2
      }

      this.executionLogs = [
        {
          timestamp: startTime.format('HH:mm:ss'),
          level: 'info',
          message: '开始执行合规检查任务'
        },
        {
          timestamp: startTime.add(30, 'seconds').format('HH:mm:ss'),
          level: 'info',
          message: '正在执行搜索条件 1: 普通搜索'
        },
        {
          timestamp: startTime.add(45, 'seconds').format('HH:mm:ss'),
          level: 'warn',
          message: '发现 3 个违规项'
        },
        {
          timestamp: startTime.add(60, 'seconds').format('HH:mm:ss'),
          level: 'info',
          message: '正在执行搜索条件 2: 关系搜索'
        },
        {
          timestamp: startTime.add(90, 'seconds').format('HH:mm:ss'),
          level: 'info',
          message: '搜索条件执行完成，正在生成报告'
        }
      ]
    },

    // 获取运行时长
    getRunningDuration() {
      if (!this.taskStatus.startTime) return '-'

      const startTime = moment(this.taskStatus.startTime)
      const now = moment()
      const duration = moment.duration(now.diff(startTime))

      const hours = Math.floor(duration.asHours())
      const minutes = duration.minutes()
      const seconds = duration.seconds()

      if (hours > 0) {
        return `${hours}小时${minutes}分钟${seconds}秒`
      } else if (minutes > 0) {
        return `${minutes}分钟${seconds}秒`
      } else {
        return `${seconds}秒`
      }
    }
  }
}
</script>

<style lang="less" scoped>
.task-monitor {
  padding: 16px 0;

  .monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .monitor-content {
    .status-card,
    .log-card,
    .stats-card {
      margin-bottom: 16px;
    }

    .status-info {
      .status-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .status-label {
          width: 80px;
          font-weight: 500;
          color: #666;
        }
      }
    }

    .log-container {
      max-height: 300px;
      overflow-y: auto;
      background-color: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 4px;
      padding: 12px;

      .log-item {
        display: flex;
        margin-bottom: 8px;
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        font-size: 12px;

        .log-time {
          width: 80px;
          color: #666;
          margin-right: 8px;
        }

        .log-level {
          width: 50px;
          font-weight: bold;
          margin-right: 8px;
        }

        .log-message {
          flex: 1;
        }

        &.log-info .log-level {
          color: #1890ff;
        }

        &.log-warn .log-level {
          color: #fa8c16;
        }

        &.log-error .log-level {
          color: #ff4d4f;
        }
      }

      .log-empty {
        text-align: center;
        color: #999;
        font-style: italic;
        padding: 20px;
      }
    }
  }
}
</style>
