<template>
  <a-modal
    :title="isEdit ? $t('cmdb.complianceCheck.editTask') : $t('cmdb.complianceCheck.createTask')"
    :visible="visible"
    :width="1200"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <a-steps :current="currentStep" class="task-form-steps">
      <a-step :title="$t('cmdb.complianceCheck.basicInfo')" />
      <a-step :title="$t('cmdb.complianceCheck.searchConditions')" />
      <a-step :title="$t('cmdb.complianceCheck.executionPlan')" />
      <a-step :title="$t('cmdb.complianceCheck.notificationSettings')" />
    </a-steps>

    <div class="task-form-content">
      <!-- 步骤1: 基本信息 -->
      <div v-show="currentStep === 0" class="step-content">
        <a-form-model :model="form" :rules="rules" ref="basicForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-model-item :label="$t('cmdb.complianceCheck.taskName')" prop="name">
            <a-input v-model="form.name" :placeholder="$t('cmdb.complianceCheck.taskNamePlaceholder')" />
          </a-form-model-item>
          <a-form-model-item :label="$t('cmdb.complianceCheck.taskDescription')" prop="description">
            <a-textarea
              v-model="form.description"
              :rows="3"
              :placeholder="$t('cmdb.complianceCheck.taskDescriptionPlaceholder')"
            />
          </a-form-model-item>
        </a-form-model>
      </div>

      <!-- 步骤2: 搜索条件 -->
      <div v-show="currentStep === 1" class="step-content">
        <div class="search-conditions">
          <div
            v-for="(search, index) in form.searches"
            :key="index"
            class="search-condition-item"
          >
            <div class="search-condition-header">
              <h4>{{ $t('cmdb.complianceCheck.searchCondition') }} {{ index + 1 }}</h4>
              <div class="search-condition-controls">
                <a-select v-model="search.type" style="width: 120px; margin-right: 8px;" @change="handleSearchTypeChange(index)">
                  <a-select-option value="resource">{{ $t('cmdb.complianceCheck.resourceSearch') }}</a-select-option>
                  <a-select-option value="relation">{{ $t('cmdb.complianceCheck.relationSearch') }}</a-select-option>
                </a-select>
                <a-button
                  type="danger"
                  size="small"
                  @click="removeSearch(index)"
                  :disabled="form.searches.length <= 1"
                >
                  {{ $t('cmdb.complianceCheck.removeSearchCondition') }}
                </a-button>
              </div>
            </div>

            <!-- 普通搜索配置 -->
            <ResourceSearchConfig
              v-if="search.type === 'resource'"
              v-model="search.config"
              :CITypeGroup="CITypeGroup"
            />

            <!-- 关系搜索配置 -->
            <RelationSearchConfig
              v-else
              v-model="search.config"
              :CITypeGroup="CITypeGroup"
            />
          </div>

          <a-button type="dashed" @click="addSearch" style="width: 100%; margin-top: 16px;">
            <a-icon type="plus" />
            {{ $t('cmdb.complianceCheck.addSearchCondition') }}
          </a-button>
        </div>
      </div>

      <!-- 步骤3: 执行计划 -->
      <div v-show="currentStep === 2" class="step-content">
        <a-form-model :model="form.schedule" ref="scheduleForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-model-item :label="$t('cmdb.complianceCheck.executionType')">
            <a-radio-group v-model="form.schedule.type">
              <a-radio value="once">{{ $t('cmdb.complianceCheck.executeOnce') }}</a-radio>
              <a-radio value="daily">{{ $t('cmdb.complianceCheck.executeDaily') }}</a-radio>
              <a-radio value="weekly">{{ $t('cmdb.complianceCheck.executeWeekly') }}</a-radio>
              <a-radio value="monthly">{{ $t('cmdb.complianceCheck.executeMonthly') }}</a-radio>
            </a-radio-group>
          </a-form-model-item>

          <template v-if="form.schedule.type !== 'once'">
            <a-form-model-item :label="$t('cmdb.complianceCheck.executionTime')">
              <a-time-picker v-model="form.schedule.time" format="HH:mm" />
            </a-form-model-item>

            <a-form-model-item v-if="form.schedule.type === 'weekly'" :label="$t('cmdb.complianceCheck.executionDate')">
              <a-checkbox-group v-model="form.schedule.weekdays">
                <a-checkbox value="1">{{ $t('cmdb.complianceCheck.monday') }}</a-checkbox>
                <a-checkbox value="2">{{ $t('cmdb.complianceCheck.tuesday') }}</a-checkbox>
                <a-checkbox value="3">{{ $t('cmdb.complianceCheck.wednesday') }}</a-checkbox>
                <a-checkbox value="4">{{ $t('cmdb.complianceCheck.thursday') }}</a-checkbox>
                <a-checkbox value="5">{{ $t('cmdb.complianceCheck.friday') }}</a-checkbox>
                <a-checkbox value="6">{{ $t('cmdb.complianceCheck.saturday') }}</a-checkbox>
                <a-checkbox value="0">{{ $t('cmdb.complianceCheck.sunday') }}</a-checkbox>
              </a-checkbox-group>
            </a-form-model-item>

            <a-form-model-item v-if="form.schedule.type === 'monthly'" :label="$t('cmdb.complianceCheck.executionDate')">
              <a-select v-model="form.schedule.monthday" style="width: 200px">
                <a-select-option v-for="day in 31" :key="day" :value="day">
                  {{ $t('cmdb.complianceCheck.monthDay', { day }) }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </template>
        </a-form-model>
      </div>

      <!-- 步骤4: 通知设置 -->
      <div v-show="currentStep === 3" class="step-content">
        <a-form-model :model="form.notification" ref="notificationForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-model-item :label="$t('cmdb.complianceCheck.enableNotification')">
            <a-switch v-model="form.notification.enabled" />
          </a-form-model-item>

          <template v-if="form.notification.enabled">
            <a-form-model-item :label="$t('cmdb.complianceCheck.notificationEmails')">
              <a-select
                v-model="form.notification.emails"
                mode="tags"
                style="width: 100%"
                :placeholder="$t('cmdb.complianceCheck.notificationEmailsPlaceholder')"
              />
            </a-form-model-item>

            <a-form-model-item :label="$t('cmdb.complianceCheck.notificationCondition')">
              <a-radio-group v-model="form.notification.condition">
                <a-radio value="always">{{ $t('cmdb.complianceCheck.alwaysNotify') }}</a-radio>
                <a-radio value="violation">{{ $t('cmdb.complianceCheck.violationOnlyNotify') }}</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </template>
        </a-form-model>
      </div>
    </div>

    <template slot="footer">
      <a-button v-if="currentStep > 0" @click="prevStep">{{ $t('cmdb.complianceCheck.previousStep') }}</a-button>
      <a-button v-if="currentStep < 3" type="primary" @click="nextStep">{{ $t('cmdb.complianceCheck.nextStep') }}</a-button>
      <a-button v-if="currentStep === 3" type="primary" @click="handleSubmit" :loading="submitting">
        {{ isEdit ? $t('cmdb.complianceCheck.update') : $t('cmdb.complianceCheck.create') }}
      </a-button>
      <a-button @click="handleCancel">{{ $t('cmdb.complianceCheck.cancel') }}</a-button>
    </template>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { createComplianceTask, updateComplianceTask } from '@/modules/cmdb/api/complianceCheck'
import ResourceSearchConfig from './ResourceSearchConfig.vue'
import RelationSearchConfig from './RelationSearchConfig.vue'

export default {
  name: 'TaskForm',
  components: {
    ResourceSearchConfig,
    RelationSearchConfig
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    taskData: {
      type: Object,
      default: null
    },
    CITypeGroup: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentStep: 0,
      submitting: false,
      form: {
        name: '',
        description: '',
        searches: [
          {
            type: 'resource',
            config: {
              searchValue: '',
              selectCITypeIds: [],
              expression: ''
            }
          }
        ],
        schedule: {
          type: 'once',
          time: moment('09:00', 'HH:mm'),
          weekdays: ['1'],
          monthday: 1
        },
        notification: {
          enabled: false,
          emails: [],
          condition: 'violation'
        }
      },
      rules: {
        name: [
          { required: true, message: this.$t('cmdb.complianceCheck.taskNamePlaceholder'), trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      } else {
        this.resetForm()
      }
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      if (this.isEdit && this.taskData) {
        this.form = {
          ...this.taskData,
          schedule: {
            ...this.taskData.schedule,
            time: this.taskData.schedule.time ? moment(this.taskData.schedule.time, 'HH:mm') : moment('09:00', 'HH:mm')
          }
        }
      }
    },

    // 重置表单
    resetForm() {
      this.currentStep = 0
      this.form = {
        name: '',
        description: '',
        searches: [
          {
            type: 'resource',
            config: {
              searchValue: '',
              selectCITypeIds: [],
              expression: ''
            }
          }
        ],
        schedule: {
          type: 'once',
          time: moment('09:00', 'HH:mm'),
          weekdays: ['1'],
          monthday: 1
        },
        notification: {
          enabled: false,
          emails: [],
          condition: 'violation'
        }
      }
    },

    // 上一步
    prevStep() {
      this.currentStep--
    },

    // 下一步
    async nextStep() {
      // 验证当前步骤
      if (this.currentStep === 0) {
        try {
          await this.$refs.basicForm.validate()
        } catch (error) {
          return
        }
      }

      this.currentStep++
    },

    // 搜索类型变化处理
    handleSearchTypeChange(index) {
      const search = this.form.searches[index]
      if (search.type === 'resource') {
        search.config = {
          searchValue: '',
          selectCITypeIds: [],
          expression: ''
        }
      } else {
        search.config = {
          sourceCIType: undefined,
          sourceCITypeSearchValue: '',
          sourceExpression: '',
          targetCITypes: [],
          targetExpression: '',
          selectedPath: [],
          returnPath: false,
          missingSearch: ''
        }
      }
    },

    // 添加搜索条件
    addSearch() {
      this.form.searches.push({
        type: 'resource',
        config: {
          searchValue: '',
          selectCITypeIds: [],
          expression: ''
        }
      })
    },

    // 删除搜索条件
    removeSearch(index) {
      if (this.form.searches.length > 1) {
        this.form.searches.splice(index, 1)
      }
    },

    // 提交表单
    async handleSubmit() {
      this.submitting = true
      try {
        const formData = {
          ...this.form,
          schedule: {
            ...this.form.schedule,
            time: this.form.schedule.time ? this.form.schedule.time.format('HH:mm') : null
          }
        }

        if (this.isEdit) {
          await updateComplianceTask(this.taskData.id, formData)
          this.$message.success(this.$t('cmdb.complianceCheck.taskUpdatedSuccess'))
        } else {
          await createComplianceTask(formData)
          this.$message.success(this.$t('cmdb.complianceCheck.taskCreatedSuccess'))
        }

        this.$emit('submit')
      } catch (error) {
        console.error('保存任务失败:', error)
        this.$message.error('保存任务失败')
      } finally {
        this.submitting = false
      }
    },

    // 取消
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="less" scoped>
.task-form-steps {
  margin-bottom: 24px;
}

.task-form-content {
  min-height: 400px;

  .step-content {
    padding: 24px 0;
  }
}

.search-conditions {
  .search-condition-item {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;

    .search-condition-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }

      .search-condition-controls {
        display: flex;
        align-items: center;
      }
    }
  }
}
</style>
