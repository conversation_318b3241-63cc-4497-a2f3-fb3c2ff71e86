<template>
  <div class="relation-search-config">
    <a-form-model :model="config" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
      <!-- 源CI类型 -->
      <a-form-model-item :label="$t('cmdb.ciType.sourceCIType')">
        <a-select
          v-model="config.sourceCIType"
          :placeholder="$t('cmdb.ciType.sourceCITypeTips')"
          @change="handleSourceCITypeChange"
        >
          <a-select-opt-group v-for="group in CITypeGroup" :key="group.id" :label="group.name">
            <a-select-option
              v-for="ciType in group.ci_types"
              :key="ciType.id"
              :value="ciType.id"
            >
              {{ ciType.alias || ciType.name }}
            </a-select-option>
          </a-select-opt-group>
        </a-select>
      </a-form-model-item>

      <!-- 源CI搜索条件 -->
      <a-form-model-item label="源CI搜索条件">
        <a-input
          v-model="config.sourceCITypeSearchValue"
          placeholder="请输入源CI搜索关键词"
          @change="handleChange"
        />
      </a-form-model-item>

      <!-- 源CI筛选表达式 -->
      <a-form-model-item label="源CI筛选表达式">
        <a-input
          v-model="config.sourceExpression"
          placeholder="例如: status:running,cpu:>4"
          @change="handleChange"
        />
      </a-form-model-item>

      <!-- 目标CI类型 -->
      <a-form-model-item :label="$t('cmdb.ciType.dstCIType')">
        <treeselect
          v-model="config.targetCITypes"
          :multiple="true"
          :options="ciTypeOptions"
          :placeholder="$t('cmdb.ciType.dstCITypeTips')"
          @input="handleTargetCITypeChange"
        />
      </a-form-model-item>

      <!-- 目标CI筛选表达式 -->
      <a-form-model-item label="目标CI筛选表达式">
        <a-input
          v-model="config.targetExpression"
          placeholder="例如: status:running"
          @change="handleChange"
        />
      </a-form-model-item>

      <!-- 关系路径 -->
      <a-form-model-item label="关系路径">
        <a-select
          v-model="config.selectedPath"
          placeholder="请选择关系路径"
          :loading="pathLoading"
          @change="handleChange"
        >
          <a-select-option
            v-for="path in availablePaths"
            :key="path.id"
            :value="path.id"
          >
            {{ formatPathName(path) }}
          </a-select-option>
        </a-select>
      </a-form-model-item>

      <!-- 缺失关系搜索 -->
      <a-form-model-item label="缺失关系搜索">
        <a-radio-group v-model="config.missingSearch" @change="handleChange">
          <a-radio value="">不启用</a-radio>
          <a-radio value="upstream">上游缺失</a-radio>
          <a-radio value="downstream">下游缺失</a-radio>
        </a-radio-group>
      </a-form-model-item>

      <!-- 搜索预览 -->
      <a-form-model-item label="搜索预览">
        <div class="search-preview">
          <a-tag v-if="searchPreview" color="blue">{{ searchPreview }}</a-tag>
          <span v-else class="preview-placeholder">请配置搜索条件</span>
        </div>
      </a-form-model-item>
    </a-form-model>
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import { getCITypeRelationPath } from '@/modules/cmdb/api/CITypeRelation'

export default {
  name: 'RelationSearchConfig',
  components: {
    Treeselect
  },
  props: {
    value: {
      type: Object,
      default: () => ({
        sourceCIType: undefined,
        sourceCITypeSearchValue: '',
        sourceExpression: '',
        targetCITypes: [],
        targetExpression: '',
        selectedPath: undefined,
        missingSearch: ''
      })
    },
    CITypeGroup: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      config: {
        sourceCIType: undefined,
        sourceCITypeSearchValue: '',
        sourceExpression: '',
        targetCITypes: [],
        targetExpression: '',
        selectedPath: undefined,
        missingSearch: ''
      },
      availablePaths: [],
      pathLoading: false
    }
  },
  computed: {
    // CI类型选项
    ciTypeOptions() {
      return this.CITypeGroup.map(group => ({
        id: `group_${group.id}`,
        label: group.name,
        children: group.ci_types.map(ciType => ({
          id: ciType.id,
          label: ciType.alias || ciType.name
        }))
      }))
    },

    // 搜索预览
    searchPreview() {
      const parts = []

      if (this.config.sourceCIType) {
        parts.push(`源类型: ${this.getCITypeName(this.config.sourceCIType)}`)
      }

      if (this.config.targetCITypes && this.config.targetCITypes.length > 0) {
        const targetNames = this.config.targetCITypes.map(id => this.getCITypeName(id)).join(', ')
        parts.push(`目标类型: ${targetNames}`)
      }

      if (this.config.selectedPath) {
        const path = this.availablePaths.find(p => p.id === this.config.selectedPath)
        if (path) {
          parts.push(`路径: ${this.formatPathName(path)}`)
        }
      }

      if (this.config.missingSearch) {
        const missingText = this.config.missingSearch === 'upstream' ? '上游缺失' : '下游缺失'
        parts.push(`缺失关系: ${missingText}`)
      }

      return parts.join(' | ')
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.config = { ...val }
        }
      }
    }
  },
  methods: {
    // 配置变化处理
    handleChange() {
      this.$emit('input', { ...this.config })
    },

    // 源CI类型变化
    async handleSourceCITypeChange() {
      this.config.selectedPath = undefined
      this.availablePaths = []
      await this.loadRelationPaths()
      this.handleChange()
    },

    // 目标CI类型变化
    async handleTargetCITypeChange() {
      this.config.selectedPath = undefined
      this.availablePaths = []
      await this.loadRelationPaths()
      this.handleChange()
    },

    // 加载关系路径
    async loadRelationPaths() {
      if (!this.config.sourceCIType || !this.config.targetCITypes || this.config.targetCITypes.length === 0) {
        return
      }

      this.pathLoading = true
      try {
        const response = await getCITypeRelationPath({
          source_type_id: this.config.sourceCIType,
          target_type_ids: this.config.targetCITypes.join(',')
        })
        this.availablePaths = response.paths || []
      } catch (error) {
        console.error('加载关系路径失败:', error)
        this.$message.error('加载关系路径失败')
      } finally {
        this.pathLoading = false
      }
    },

    // 获取CI类型名称
    getCITypeName(ciTypeId) {
      for (const group of this.CITypeGroup) {
        const ciType = group.ci_types.find(ct => ct.id === ciTypeId)
        if (ciType) {
          return ciType.alias || ciType.name
        }
      }
      return `CI类型${ciTypeId}`
    },

    // 格式化路径名称
    formatPathName(path) {
      if (!path || !path.path) return ''

      return path.path.map(step => {
        const sourceName = this.getCITypeName(step.source_type_id)
        const targetName = this.getCITypeName(step.target_type_id)
        return `${sourceName} → ${targetName}`
      }).join(' → ')
    }
  }
}
</script>

<style lang="less" scoped>
.relation-search-config {
  .search-preview {
    .preview-placeholder {
      color: #999;
      font-style: italic;
    }
  }
}
</style>
