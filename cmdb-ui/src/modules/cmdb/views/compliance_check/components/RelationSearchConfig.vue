<template>
  <div class="relation-search-config">
    <!-- 复用现有的关系搜索条件组件 -->
    <SearchCondition
      :CITypeGroup="CITypeGroup"
      :sourceCIType="config.sourceCIType"
      :sourceCITypeSearchValue="config.sourceCITypeSearchValue"
      :sourceAllAttributesList="sourceAllAttributesList"
      :sourceExpression="config.sourceExpression"
      :targetCITypes="config.targetCITypes"
      :targetCITypeGroup="targetCITypeGroup"
      :targetAllAttributesList="targetAllAttributesList"
      :targetExpression="config.targetExpression"
      :returnPath="config.returnPath"
      :missingSearch="config.missingSearch"
      :allPath="allPath"
      :selectedPath="config.selectedPath"
      :isSearch="false"
      :isSearchLoading="false"
      @changeData="handleChangeData"
      @search="handleSearch"
    />

    <!-- 搜索预览 -->
    <div class="search-preview-section">
      <a-form-model-item label="搜索预览" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <div class="search-preview">
          <a-tag v-if="searchPreview" color="blue">{{ searchPreview }}</a-tag>
          <span v-else class="preview-placeholder">请配置搜索条件</span>
        </div>
      </a-form-model-item>
    </div>
  </div>
</template>

<script>
import SearchCondition from '@/modules/cmdb/views/resource_search_2/relationSearch/components/searchCondition.vue'
import { getCITypeRelationPath } from '@/modules/cmdb/api/CITypeRelation'
import { getCITypeAttributesByTypeIds } from '@/modules/cmdb/api/CITypeAttr'

export default {
  name: 'RelationSearchConfig',
  components: {
    SearchCondition
  },
  props: {
    value: {
      type: Object,
      default: () => ({
        sourceCIType: undefined,
        sourceCITypeSearchValue: '',
        sourceExpression: '',
        targetCITypes: [],
        targetExpression: '',
        selectedPath: [],
        returnPath: false,
        missingSearch: ''
      })
    },
    CITypeGroup: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      config: {
        sourceCIType: undefined,
        sourceCITypeSearchValue: '',
        sourceExpression: '',
        targetCITypes: [],
        targetExpression: '',
        selectedPath: [],
        returnPath: false,
        missingSearch: ''
      },
      sourceAllAttributesList: [],
      targetAllAttributesList: [],
      targetCITypeGroup: {},
      allPath: []
    }
  },
  computed: {
    // 搜索预览
    searchPreview() {
      const parts = []

      if (this.config.sourceCIType) {
        parts.push(`源类型: ${this.getCITypeName(this.config.sourceCIType)}`)
      }

      if (this.config.targetCITypes && this.config.targetCITypes.length > 0) {
        const targetNames = this.config.targetCITypes.map(id => this.getCITypeName(id)).join(', ')
        parts.push(`目标类型: ${targetNames}`)
      }

      if (this.config.selectedPath && this.config.selectedPath.length > 0) {
        const pathNames = this.allPath
          .filter(path => this.config.selectedPath.includes(path.value))
          .map(path => path.pathNames)
          .join(', ')
        parts.push(`路径: ${pathNames}`)
      }

      if (this.config.missingSearch) {
        const missingText = this.config.missingSearch === 'up' ? '上游缺失' : '下游缺失'
        parts.push(`缺失关系: ${missingText}`)
      }

      return parts.join(' | ')
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.config = { ...val }
        }
      }
    }
  },
  methods: {
    // 处理搜索条件变化
    handleChangeData(data) {
      this.config[data.name] = data.value
      this.$emit('input', { ...this.config })

      // 当源CI类型或目标CI类型变化时，重新加载路径
      if (data.name === 'sourceCIType' || data.name === 'targetCITypes') {
        this.loadRelationPaths()
        this.loadCITypeAttributes()
      }
    },

    // 处理搜索（在配置模式下不执行实际搜索）
    handleSearch() {
      // 在合规检查配置中，不需要实际执行搜索
      this.$emit('input', { ...this.config })
    },

    // 加载关系路径
    async loadRelationPaths() {
      if (!this.config.sourceCIType || !this.config.targetCITypes || this.config.targetCITypes.length === 0) {
        this.allPath = []
        return
      }

      try {
        const response = await getCITypeRelationPath({
          source_type_id: this.config.sourceCIType,
          target_type_ids: this.config.targetCITypes.join(',')
        })
        this.allPath = response.paths || []

        // 构建目标CI类型分组
        this.buildTargetCITypeGroup()
      } catch (error) {
        console.error('加载关系路径失败:', error)
        this.$message.error('加载关系路径失败')
        this.allPath = []
      }
    },

    // 构建目标CI类型分组
    buildTargetCITypeGroup() {
      this.targetCITypeGroup = {}
      if (this.allPath.length > 0) {
        // 根据路径长度分组
        this.allPath.forEach(path => {
          const level = path.path ? path.path.length : 1
          if (!this.targetCITypeGroup[level]) {
            this.targetCITypeGroup[level] = []
          }

          // 添加目标CI类型
          if (path.path) {
            path.path.forEach(step => {
              const targetType = this.getCITypeById(step.target_type_id)
              if (targetType && !this.targetCITypeGroup[level].find(t => t.id === targetType.id)) {
                this.targetCITypeGroup[level].push(targetType)
              }
            })
          }
        })
      }
    },

    // 加载CI类型属性
    async loadCITypeAttributes() {
      try {
        // 加载源CI类型属性
        if (this.config.sourceCIType) {
          const sourceResponse = await getCITypeAttributesByTypeIds([this.config.sourceCIType])
          this.sourceAllAttributesList = sourceResponse.attributes || []
        }

        // 加载目标CI类型属性
        if (this.config.targetCITypes && this.config.targetCITypes.length > 0) {
          const targetResponse = await getCITypeAttributesByTypeIds(this.config.targetCITypes)
          this.targetAllAttributesList = targetResponse.attributes || []
        }
      } catch (error) {
        console.error('加载CI类型属性失败:', error)
      }
    },

    // 获取CI类型名称
    getCITypeName(ciTypeId) {
      const ciType = this.getCITypeById(ciTypeId)
      return ciType ? (ciType.alias || ciType.name) : `CI类型${ciTypeId}`
    },

    // 根据ID获取CI类型
    getCITypeById(ciTypeId) {
      for (const group of this.CITypeGroup) {
        const ciType = group.ci_types.find(ct => ct.id === ciTypeId)
        if (ciType) {
          return ciType
        }
      }
      return null
    }
  }
}
</script>

<style lang="less" scoped>
.relation-search-config {
  // 覆盖SearchCondition组件的样式，适配配置模式
  /deep/ .search-condition {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
    background-color: #fafafa;
    margin-bottom: 16px;

    .search-condition-favor {
      display: none; // 隐藏收藏功能
    }

    .search-condition-submit {
      display: none; // 隐藏搜索按钮
    }
  }

  .search-preview-section {
    .search-preview {
      .preview-placeholder {
        color: #999;
        font-style: italic;
      }
    }
  }
}
</style>
