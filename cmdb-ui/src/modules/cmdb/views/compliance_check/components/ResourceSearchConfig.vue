<template>
  <div class="resource-search-config">
    <a-form-model :model="config" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
      <!-- 搜索关键词 -->
      <a-form-model-item :label="$t('cmdb.complianceCheck.keyword')">
        <a-input
          v-model="config.searchValue"
          :placeholder="$t('cmdb.ciType.searchInputTip')"
          @change="handleChange"
        />
      </a-form-model-item>

      <!-- CI类型选择 -->
      <a-form-model-item :label="$t('cmdb.ciType.CIType')">
        <treeselect
          v-model="config.selectCITypeIds"
          :multiple="true"
          :options="ciTypeOptions"
          :placeholder="$t('cmdb.ciType.selectCIType')"
          @input="handleChange"
        />
      </a-form-model-item>

      <!-- 高级筛选条件 -->
      <a-form-model-item :label="$t('cmdb.ciType.conditionFilter')">
        <div class="filter-expression">
          <a-input
            v-model="config.expression"
            :placeholder="$t('cmdb.components.ciSearchTips2')"
            @change="handleChange"
          />
          <a-button
            type="link"
            @click="showFilterBuilder"
            style="padding: 0; margin-left: 8px;"
          >
            {{ $t('cmdb.ciType.advancedFilter') }}
          </a-button>
        </div>
      </a-form-model-item>

      <!-- 搜索预览 -->
      <a-form-model-item label="搜索预览">
        <div class="search-preview">
          <a-tag v-if="searchPreview" color="blue">{{ searchPreview }}</a-tag>
          <span v-else class="preview-placeholder">请配置搜索条件</span>
        </div>
      </a-form-model-item>
    </a-form-model>

    <!-- 高级筛选构建器弹窗 -->
    <a-modal
      title="高级筛选条件"
      :visible="filterBuilderVisible"
      :width="800"
      @ok="handleFilterBuilderOk"
      @cancel="handleFilterBuilderCancel"
    >
      <div class="filter-builder">
        <p>请使用以下格式构建筛选条件：</p>
        <ul>
          <li>属性名:值 (例如: hostname:server01)</li>
          <li>属性名:>值 (大于，例如: cpu:>4)</li>
          <li>属性名:<值 (小于，例如: memory:<8)</li>
          <li>属性名:>=值 (大于等于)</li>
          <li>属性名:<=值 (小于等于)</li>
          <li>多个条件用逗号分隔 (例如: cpu:>4,memory:>=8)</li>
        </ul>
        <a-textarea
          v-model="tempExpression"
          :rows="4"
          placeholder="例如: cpu:>4,memory:>=8,status:running"
        />
      </div>
    </a-modal>
  </div>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'

export default {
  name: 'ResourceSearchConfig',
  components: {
    Treeselect
  },
  props: {
    value: {
      type: Object,
      default: () => ({
        searchValue: '',
        selectCITypeIds: [],
        expression: ''
      })
    },
    CITypeGroup: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      config: {
        searchValue: '',
        selectCITypeIds: [],
        expression: ''
      },
      filterBuilderVisible: false,
      tempExpression: ''
    }
  },
  computed: {
    // CI类型选项
    ciTypeOptions() {
      return this.CITypeGroup.map(group => ({
        id: `group_${group.id}`,
        label: group.name,
        children: group.ci_types.map(ciType => ({
          id: ciType.id,
          label: ciType.alias || ciType.name
        }))
      }))
    },

    // 搜索预览
    searchPreview() {
      const parts = []

      // CI类型
      if (this.config.selectCITypeIds && this.config.selectCITypeIds.length > 0) {
        parts.push(`_type:(${this.config.selectCITypeIds.join(';')})`)
      }

      // 高级筛选
      if (this.config.expression) {
        parts.push(this.config.expression)
      }

      // 搜索关键词
      if (this.config.searchValue) {
        parts.push(`*${this.config.searchValue}*`)
      }

      return parts.length > 0 ? `q=${parts.join(',')}` : ''
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.config = { ...val }
        }
      }
    }
  },
  methods: {
    // 配置变化处理
    handleChange() {
      this.$emit('input', { ...this.config })
    },

    // 显示筛选条件构建器
    showFilterBuilder() {
      this.tempExpression = this.config.expression
      this.filterBuilderVisible = true
    },

    // 筛选条件构建器确认
    handleFilterBuilderOk() {
      this.config.expression = this.tempExpression
      this.filterBuilderVisible = false
      this.handleChange()
    },

    // 筛选条件构建器取消
    handleFilterBuilderCancel() {
      this.filterBuilderVisible = false
      this.tempExpression = this.config.expression
    }
  }
}
</script>

<style lang="less" scoped>
.resource-search-config {
  .filter-expression {
    display: flex;
    align-items: center;
  }

  .search-preview {
    .preview-placeholder {
      color: #999;
      font-style: italic;
    }
  }
}

.filter-builder {
  ul {
    margin: 16px 0;
    padding-left: 20px;

    li {
      margin-bottom: 4px;
      color: #666;
    }
  }
}
</style>
