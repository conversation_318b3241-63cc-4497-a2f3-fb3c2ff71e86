<template>
  <a-card :bordered="false">
    <div class="action-btn">
      <a-button @click="createTask" type="primary" style="margin-bottom: 15px;">
        <a-icon type="plus" />
        {{ $t('cmdb.complianceCheck.createTask') }}
      </a-button>
    </div>

    <!-- 搜索区域 -->
    <div class="table-search" style="margin-bottom: 15px;">
      <a-form layout="inline">
        <a-form-item :label="$t('cmdb.complianceCheck.taskName')">
          <a-input v-model="searchParams.name" @change="handleSearch" allowClear />
        </a-form-item>
        <a-form-item :label="$t('cmdb.complianceCheck.taskStatus')">
          <a-select v-model="searchParams.status" style="width: 120px" @change="handleSearch" allowClear>
            <a-select-option value="enabled">{{ $t('cmdb.complianceCheck.enabled') }}</a-select-option>
            <a-select-option value="disabled">{{ $t('cmdb.complianceCheck.disabled') }}</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </div>

    <div>
      <vxe-table
        ref="taskTable"
        :data="taskList"
        :loading="loading"
        keep-source
        highlight-hover-row
        stripe
        class="ops-stripe-table"
        bordered
        :height="`${windowHeight - 300}px`"
      >
        <vxe-table-column field="name" :title="$t('cmdb.complianceCheck.taskName')" min-width="200"></vxe-table-column>
        <vxe-table-column field="description" :title="$t('cmdb.complianceCheck.taskDescription')" min-width="250" show-overflow></vxe-table-column>

        <vxe-table-column field="enabled" :title="$t('cmdb.complianceCheck.enabled')" width="100">
          <template #default="{row}">
            <a-badge
              :status="row.enabled ? 'success' : 'default'"
              :text="row.enabled ? $t('cmdb.complianceCheck.enabled') : $t('cmdb.complianceCheck.disabled')"
            />
          </template>
        </vxe-table-column>

        <vxe-table-column field="status" :title="$t('cmdb.complianceCheck.taskStatus')" width="100">
          <template #default="{row}">
            <a-badge :status="getStatusBadge(row.status)" :text="getStatusText(row.status)" />
          </template>
        </vxe-table-column>

        <vxe-table-column field="lastExecuteTime" :title="$t('cmdb.complianceCheck.lastExecuteTime')" width="180"></vxe-table-column>
        <vxe-table-column field="nextExecuteTime" :title="$t('cmdb.complianceCheck.nextExecuteTime')" width="180"></vxe-table-column>
        <vxe-table-column field="createTime" :title="$t('cmdb.complianceCheck.createTime')" width="180"></vxe-table-column>

        <vxe-table-column field="operation" :title="$t('operation')" align="center" width="300">
          <template #default="{row}">
            <a-button-group size="small">
              <a-button @click="viewTask(row)">{{ $t('cmdb.complianceCheck.viewTask') }}</a-button>
              <a-button @click="editTask(row)">{{ $t('cmdb.complianceCheck.editTask') }}</a-button>
              <a-button @click="executeTask(row)" :loading="row.executing">
                {{ $t('cmdb.complianceCheck.executeTask') }}
              </a-button>
              <a-dropdown>
                <a-menu slot="overlay">
                  <a-menu-item @click="toggleTaskStatus(row)">
                    {{ row.enabled ? $t('cmdb.complianceCheck.disableTask') : $t('cmdb.complianceCheck.enableTask') }}
                  </a-menu-item>
                  <a-menu-item @click="deleteTask(row)">
                    {{ $t('cmdb.complianceCheck.deleteTask') }}
                  </a-menu-item>
                </a-menu>
                <a-button size="small">
                  {{ $t('cmdb.complianceCheck.more') }} <a-icon type="down" />
                </a-button>
              </a-dropdown>
            </a-button-group>
          </template>
        </vxe-table-column>
      </vxe-table>

      <pager
        :current-page.sync="queryParams.page"
        :page-size.sync="queryParams.page_size"
        :page-sizes="[20, 50, 100]"
        :total="pagination.total"
        :isLoading="loading"
        @change="handlePageChange"
        @showSizeChange="handleSizeChange"
        :style="{ marginTop: '10px' }"
      />
    </div>

    <!-- 任务表单弹窗 -->
    <TaskForm
      :visible="taskFormVisible"
      :isEdit="isEditMode"
      :taskData="currentTask"
      :CITypeGroup="CITypeGroup"
      @submit="handleTaskSubmit"
      @cancel="handleTaskCancel"
    />

    <!-- 任务详情抽屉 -->
    <TaskDetail
      :visible="taskDetailVisible"
      :task="currentTask"
      @close="handleTaskDetailClose"
    />
  </a-card>
</template>

<script>
import { mapState } from 'vuex'
import Pager from '@/components/Pager'
import {
  getComplianceTasks,
  deleteComplianceTask,
  executeComplianceTask,
  toggleComplianceTask
} from '@/modules/cmdb/api/complianceCheck'
import { getCITypes } from '@/modules/cmdb/api/CIType'
import { getStatusBadge, getStatusText } from './utils/scheduleUtils'
import { mockTasks, mockCITypeGroup } from './mockData'
import TaskForm from './components/TaskForm.vue'
import TaskDetail from './components/TaskDetail.vue'

export default {
  name: 'ComplianceCheck',
  components: {
    Pager,
    TaskForm,
    TaskDetail
  },
  data() {
    return {
      loading: false,
      taskList: [],
      CITypeGroup: [],
      taskFormVisible: false,
      taskDetailVisible: false,
      isEditMode: false,
      currentTask: null,
      searchParams: {
        name: '',
        status: undefined
      },
      queryParams: {
        page: 1,
        page_size: 20
      },
      pagination: {
        total: 0
      }
    }
  },
  computed: {
    ...mapState({
      windowHeight: state => state.windowHeight
    })
  },
  async mounted() {
    await this.loadCITypes()
    await this.loadTasks()
  },
  methods: {
    getStatusBadge,
    getStatusText,

    // 加载CI类型
    async loadCITypes() {
      try {
        const response = await getCITypes()
        this.CITypeGroup = response.groups || []

        // 如果API返回为空，使用模拟数据作为后备
        if (this.CITypeGroup.length === 0) {
          this.CITypeGroup = mockCITypeGroup
        }
      } catch (error) {
        console.error('加载CI类型失败:', error)
        // 使用模拟数据作为后备
        this.CITypeGroup = mockCITypeGroup
        this.$message.warning('加载CI类型失败，使用模拟数据')
      }
    },

    // 加载任务列表
    async loadTasks() {
      this.loading = true
      try {
        const params = {
          ...this.queryParams,
          ...this.searchParams
        }

        try {
          const response = await getComplianceTasks(params)
          this.taskList = response.tasks || []
          this.pagination.total = response.total || 0
        } catch (apiError) {
          // 如果API调用失败，使用模拟数据
          console.warn('API调用失败，使用模拟数据:', apiError)
          let filteredTasks = [...mockTasks]

          // 应用搜索过滤
          if (this.searchParams.name) {
            filteredTasks = filteredTasks.filter(task =>
              task.name.toLowerCase().includes(this.searchParams.name.toLowerCase())
            )
          }
          if (this.searchParams.status) {
            filteredTasks = filteredTasks.filter(task =>
              this.searchParams.status === 'enabled' ? task.enabled : !task.enabled
            )
          }

          this.taskList = filteredTasks
          this.pagination.total = filteredTasks.length
        }
      } catch (error) {
        console.error('加载任务列表失败:', error)
        this.$message.error('加载任务列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索处理
    handleSearch() {
      this.queryParams.page = 1
      this.loadTasks()
    },

    // 分页变化处理
    handlePageChange(page) {
      this.queryParams.page = page
      this.loadTasks()
    },

    // 页面大小变化处理
    handleSizeChange(page, size) {
      this.queryParams.page = page
      this.queryParams.page_size = size
      this.loadTasks()
    },

    // 创建任务
    createTask() {
      this.isEditMode = false
      this.currentTask = null
      this.taskFormVisible = true
    },

    // 编辑任务
    editTask(task) {
      this.isEditMode = true
      this.currentTask = { ...task }
      this.taskFormVisible = true
    },

    // 查看任务
    viewTask(task) {
      this.currentTask = task
      this.taskDetailVisible = true
    },

    // 执行任务
    async executeTask(task) {
      this.$confirm({
        title: this.$t('cmdb.complianceCheck.confirmExecuteTask', { taskName: task.name }),
        onOk: async () => {
          try {
            this.$set(task, 'executing', true)
            await executeComplianceTask(task.id)
            this.$message.success(this.$t('cmdb.complianceCheck.taskExecutedSuccess'))
            await this.loadTasks()
          } catch (error) {
            console.error('执行任务失败:', error)
            this.$message.error('执行任务失败')
          } finally {
            this.$set(task, 'executing', false)
          }
        }
      })
    },

    // 切换任务状态
    async toggleTaskStatus(task) {
      const action = task.enabled ? this.$t('cmdb.complianceCheck.disableTask') : this.$t('cmdb.complianceCheck.enableTask')
      this.$confirm({
        title: this.$t('cmdb.complianceCheck.confirmToggleTaskStatus', { action, taskName: task.name }),
        onOk: async () => {
          try {
            await toggleComplianceTask(task.id, !task.enabled)
            this.$message.success(this.$t('cmdb.complianceCheck.taskStatusUpdatedSuccess'))
            await this.loadTasks()
          } catch (error) {
            console.error('更新任务状态失败:', error)
            this.$message.error('更新任务状态失败')
          }
        }
      })
    },

    // 删除任务
    deleteTask(task) {
      this.$confirm({
        title: this.$t('cmdb.complianceCheck.confirmDeleteTask', { taskName: task.name }),
        onOk: async () => {
          try {
            await deleteComplianceTask(task.id)
            this.$message.success(this.$t('cmdb.complianceCheck.taskDeletedSuccess'))
            await this.loadTasks()
          } catch (error) {
            console.error('删除任务失败:', error)
            this.$message.error('删除任务失败')
          }
        }
      })
    },

    // 任务表单提交
    async handleTaskSubmit() {
      this.taskFormVisible = false
      await this.loadTasks()
    },

    // 任务表单取消
    handleTaskCancel() {
      this.taskFormVisible = false
      this.currentTask = null
    },

    // 任务详情关闭
    handleTaskDetailClose() {
      this.taskDetailVisible = false
      this.currentTask = null
    }
  }
}
</script>

<style lang="less" scoped>
.table-search {
  .ant-form-item {
    margin-bottom: 0;
  }
}
</style>
