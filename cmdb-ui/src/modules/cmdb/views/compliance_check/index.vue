<template>
  <div class="compliance-check">
    <!-- 页面标题和操作按钮 -->
    <div class="compliance-check-header">
      <h2>{{ $t('cmdb.complianceCheck.title') }}</h2>
      <a-button type="primary" @click="createTask">
        <a-icon type="plus" />
        {{ $t('cmdb.complianceCheck.createTask') }}
      </a-button>
    </div>

    <!-- 任务列表表格 -->
    <a-table
      :columns="taskColumns"
      :dataSource="taskList"
      :pagination="pagination"
      :loading="loading"
      rowKey="id"
      @change="handleTableChange"
    >
      <!-- 任务状态列 -->
      <template slot="status" slot-scope="status">
        <a-badge :status="getStatusBadge(status)" :text="getStatusText(status)" />
      </template>

      <!-- 启用状态列 -->
      <template slot="enabled" slot-scope="enabled">
        <a-badge
          :status="enabled ? 'success' : 'default'"
          :text="enabled ? $t('cmdb.complianceCheck.enabled') : $t('cmdb.complianceCheck.disabled')"
        />
      </template>

      <!-- 操作列 -->
      <template slot="actions" slot-scope="text, record">
        <a-button-group size="small">
          <a-button @click="viewTask(record)">{{ $t('cmdb.complianceCheck.viewTask') }}</a-button>
          <a-button @click="editTask(record)">{{ $t('cmdb.complianceCheck.editTask') }}</a-button>
          <a-button @click="executeTask(record)" :loading="record.executing">
            {{ $t('cmdb.complianceCheck.executeTask') }}
          </a-button>
          <a-dropdown>
            <a-menu slot="overlay">
              <a-menu-item @click="toggleTaskStatus(record)">
                {{ record.enabled ? $t('cmdb.complianceCheck.disableTask') : $t('cmdb.complianceCheck.enableTask') }}
              </a-menu-item>
              <a-menu-item @click="deleteTask(record)">
                {{ $t('cmdb.complianceCheck.deleteTask') }}
              </a-menu-item>
            </a-menu>
            <a-button>
              {{ $t('cmdb.complianceCheck.more') }} <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </a-button-group>
      </template>
    </a-table>

    <!-- 任务表单弹窗 -->
    <TaskForm
      :visible="taskFormVisible"
      :isEdit="isEditMode"
      :taskData="currentTask"
      :CITypeGroup="CITypeGroup"
      @submit="handleTaskSubmit"
      @cancel="handleTaskCancel"
    />

    <!-- 任务详情抽屉 -->
    <TaskDetail
      :visible="taskDetailVisible"
      :task="currentTask"
      @close="handleTaskDetailClose"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  getComplianceTasks,
  deleteComplianceTask,
  executeComplianceTask,
  toggleComplianceTask
} from '@/modules/cmdb/api/complianceCheck'
import { getCITypes } from '@/modules/cmdb/api/CIType'
import { getStatusBadge, getStatusText } from './utils/scheduleUtils'
import { mockTasks, mockCITypeGroup } from './mockData'
import TaskForm from './components/TaskForm.vue'
import TaskDetail from './components/TaskDetail.vue'

export default {
  name: 'ComplianceCheck',
  components: {
    TaskForm,
    TaskDetail
  },
  data() {
    return {
      loading: false,
      taskList: [],
      CITypeGroup: [],
      taskFormVisible: false,
      taskDetailVisible: false,
      isEditMode: false,
      currentTask: null,
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`
      },
      taskColumns: [
        {
          title: this.$t('cmdb.complianceCheck.taskName'),
          dataIndex: 'name',
          key: 'name',
          width: 200
        },
        {
          title: this.$t('cmdb.complianceCheck.taskDescription'),
          dataIndex: 'description',
          key: 'description',
          ellipsis: true
        },
        {
          title: this.$t('cmdb.complianceCheck.enabled'),
          dataIndex: 'enabled',
          key: 'enabled',
          width: 100,
          scopedSlots: { customRender: 'enabled' }
        },
        {
          title: this.$t('cmdb.complianceCheck.taskStatus'),
          dataIndex: 'status',
          key: 'status',
          width: 100,
          scopedSlots: { customRender: 'status' }
        },
        {
          title: this.$t('cmdb.complianceCheck.lastExecuteTime'),
          dataIndex: 'lastExecuteTime',
          key: 'lastExecuteTime',
          width: 180
        },
        {
          title: this.$t('cmdb.complianceCheck.nextExecuteTime'),
          dataIndex: 'nextExecuteTime',
          key: 'nextExecuteTime',
          width: 180
        },
        {
          title: this.$t('cmdb.complianceCheck.createTime'),
          dataIndex: 'createTime',
          key: 'createTime',
          width: 180
        },
        {
          title: this.$t('cmdb.complianceCheck.actions'),
          key: 'actions',
          width: 300,
          fixed: 'right',
          scopedSlots: { customRender: 'actions' }
        }
      ]
    }
  },
  computed: {
    ...mapState({
      windowHeight: state => state.windowHeight
    })
  },
  async mounted() {
    await this.loadCITypes()
    await this.loadTasks()
  },
  methods: {
    getStatusBadge,
    getStatusText,

    // 加载CI类型
    async loadCITypes() {
      try {
        // 优先使用模拟数据进行演示
        this.CITypeGroup = mockCITypeGroup

        // 实际环境中使用以下代码
        // const response = await getCITypes()
        // this.CITypeGroup = response.groups || []
      } catch (error) {
        console.error('加载CI类型失败:', error)
        this.$message.error('加载CI类型失败')
      }
    },

    // 加载任务列表
    async loadTasks() {
      this.loading = true
      try {
        // 优先使用模拟数据进行演示
        this.taskList = mockTasks
        this.pagination.total = mockTasks.length

        // 实际环境中使用以下代码
        // const params = {
        //   page: this.pagination.current,
        //   page_size: this.pagination.pageSize
        // }
        // const response = await getComplianceTasks(params)
        // this.taskList = response.tasks || []
        // this.pagination.total = response.total || 0
      } catch (error) {
        console.error('加载任务列表失败:', error)
        this.$message.error('加载任务列表失败')
      } finally {
        this.loading = false
      }
    },

    // 表格变化处理
    handleTableChange(pagination) {
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.loadTasks()
    },

    // 创建任务
    createTask() {
      this.isEditMode = false
      this.currentTask = null
      this.taskFormVisible = true
    },

    // 编辑任务
    editTask(task) {
      this.isEditMode = true
      this.currentTask = { ...task }
      this.taskFormVisible = true
    },

    // 查看任务
    viewTask(task) {
      this.currentTask = task
      this.taskDetailVisible = true
    },

    // 执行任务
    async executeTask(task) {
      this.$confirm({
        title: this.$t('cmdb.complianceCheck.confirmExecuteTask', { taskName: task.name }),
        onOk: async () => {
          try {
            this.$set(task, 'executing', true)
            await executeComplianceTask(task.id)
            this.$message.success(this.$t('cmdb.complianceCheck.taskExecutedSuccess'))
            await this.loadTasks()
          } catch (error) {
            console.error('执行任务失败:', error)
            this.$message.error('执行任务失败')
          } finally {
            this.$set(task, 'executing', false)
          }
        }
      })
    },

    // 切换任务状态
    async toggleTaskStatus(task) {
      const action = task.enabled ? this.$t('cmdb.complianceCheck.disableTask') : this.$t('cmdb.complianceCheck.enableTask')
      this.$confirm({
        title: this.$t('cmdb.complianceCheck.confirmToggleTaskStatus', { action, taskName: task.name }),
        onOk: async () => {
          try {
            await toggleComplianceTask(task.id, !task.enabled)
            this.$message.success(this.$t('cmdb.complianceCheck.taskStatusUpdatedSuccess'))
            await this.loadTasks()
          } catch (error) {
            console.error('更新任务状态失败:', error)
            this.$message.error('更新任务状态失败')
          }
        }
      })
    },

    // 删除任务
    deleteTask(task) {
      this.$confirm({
        title: this.$t('cmdb.complianceCheck.confirmDeleteTask', { taskName: task.name }),
        onOk: async () => {
          try {
            await deleteComplianceTask(task.id)
            this.$message.success(this.$t('cmdb.complianceCheck.taskDeletedSuccess'))
            await this.loadTasks()
          } catch (error) {
            console.error('删除任务失败:', error)
            this.$message.error('删除任务失败')
          }
        }
      })
    },

    // 任务表单提交
    async handleTaskSubmit() {
      this.taskFormVisible = false
      await this.loadTasks()
    },

    // 任务表单取消
    handleTaskCancel() {
      this.taskFormVisible = false
      this.currentTask = null
    },

    // 任务详情关闭
    handleTaskDetailClose() {
      this.taskDetailVisible = false
      this.currentTask = null
    }
  }
}
</script>

<style lang="less" scoped>
.compliance-check {
  padding: 24px;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }
}
</style>
