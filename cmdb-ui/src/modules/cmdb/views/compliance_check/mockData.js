// 模拟数据，用于演示合规检查功能

// 模拟任务列表
export const mockTasks = [
  {
    id: 1,
    name: '服务器配置合规检查',
    description: '检查服务器CPU和内存配置是否符合标准',
    enabled: true,
    status: 'success',
    lastExecuteTime: '2024-01-15 09:00:00',
    nextExecuteTime: '2024-01-22 09:00:00',
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-15 09:30:00',
    searches: [
      {
        type: 'resource',
        config: {
          searchValue: '',
          selectCITypeIds: [1, 2],
          expression: 'cpu:<4,memory:<8'
        }
      }
    ],
    schedule: {
      type: 'weekly',
      time: '09:00',
      weekdays: ['1'],
      cron: '0 9 * * 1'
    },
    notification: {
      enabled: true,
      emails: ['<EMAIL>'],
      condition: 'violation'
    }
  },
  {
    id: 2,
    name: '网络设备状态检查',
    description: '检查网络设备的运行状态和配置',
    enabled: false,
    status: 'failed',
    lastExecuteTime: '2024-01-14 15:30:00',
    nextExecuteTime: null,
    createTime: '2024-01-05 14:00:00',
    updateTime: '2024-01-14 15:45:00',
    searches: [
      {
        type: 'resource',
        config: {
          searchValue: 'switch',
          selectCITypeIds: [3],
          expression: 'status:!running'
        }
      }
    ],
    schedule: {
      type: 'daily',
      time: '15:30',
      cron: '30 15 * * *'
    },
    notification: {
      enabled: false,
      emails: [],
      condition: 'always'
    }
  },
  {
    id: 3,
    name: '应用服务关系检查',
    description: '检查应用服务之间的依赖关系是否完整',
    enabled: true,
    status: 'running',
    lastExecuteTime: '2024-01-16 10:00:00',
    nextExecuteTime: '2024-01-17 10:00:00',
    createTime: '2024-01-10 16:00:00',
    updateTime: '2024-01-16 10:00:00',
    searches: [
      {
        type: 'relation',
        config: {
          sourceCIType: 4,
          sourceCITypeSearchValue: '',
          sourceExpression: '',
          targetCITypes: [5, 6],
          targetExpression: '',
          selectedPath: 1,
          missingSearch: 'downstream'
        }
      }
    ],
    schedule: {
      type: 'daily',
      time: '10:00',
      cron: '0 10 * * *'
    },
    notification: {
      enabled: true,
      emails: ['<EMAIL>', '<EMAIL>'],
      condition: 'violation'
    }
  }
]

// 模拟执行历史
export const mockExecutionHistory = [
  {
    id: 'E20240116001',
    taskId: 1,
    executeTime: '2024-01-16 09:00:00',
    duration: 45000,
    status: 'success',
    violationCount: 5
  },
  {
    id: 'E20240115001',
    taskId: 1,
    executeTime: '2024-01-15 09:00:00',
    duration: 38000,
    status: 'success',
    violationCount: 3
  },
  {
    id: 'E20240114001',
    taskId: 1,
    executeTime: '2024-01-14 09:00:00',
    duration: 42000,
    status: 'success',
    violationCount: 8
  }
]

// 模拟合规检查报告
export const mockReport = {
  reportId: 'R20240116001',
  taskId: 1,
  taskName: '服务器配置合规检查',
  executionId: 'E20240116001',
  executeTime: '2024-01-16 09:00:00',
  duration: 45000,
  summary: {
    totalSearches: 1,
    totalViolations: 5,
    severityBreakdown: {
      high: 2,
      medium: 2,
      low: 1
    }
  },
  results: [
    {
      searchIndex: 1,
      searchType: 'resource',
      searchConfig: {
        searchValue: '',
        selectCITypeIds: [1, 2],
        expression: 'cpu:<4,memory:<8'
      },
      violations: [
        {
          ciId: 'ci_001',
          ciType: '物理服务器',
          ciName: 'server-01',
          violationReason: 'CPU核数不足：当前2核，要求≥4核',
          severity: 'high'
        },
        {
          ciId: 'ci_002',
          ciType: '物理服务器',
          ciName: 'server-02',
          violationReason: '内存容量不足：当前4GB，要求≥8GB',
          severity: 'high'
        },
        {
          ciId: 'ci_003',
          ciType: '虚拟机',
          ciName: 'vm-web-01',
          violationReason: 'CPU核数不足：当前2核，要求≥4核',
          severity: 'medium'
        },
        {
          ciId: 'ci_004',
          ciType: '虚拟机',
          ciName: 'vm-db-01',
          violationReason: '内存容量不足：当前6GB，要求≥8GB',
          severity: 'medium'
        },
        {
          ciId: 'ci_005',
          ciType: '容器',
          ciName: 'container-app-01',
          violationReason: 'CPU限制过低：当前1核，建议≥2核',
          severity: 'low'
        }
      ],
      violationCount: 5
    }
  ],
  notification: {
    sent: true,
    sentTime: '2024-01-16 09:05:00',
    recipients: ['<EMAIL>']
  }
}

// 模拟CI类型分组
export const mockCITypeGroup = [
  {
    id: 1,
    name: '基础设施',
    ci_types: [
      { id: 1, name: 'physical_server', alias: '物理服务器' },
      { id: 2, name: 'virtual_machine', alias: '虚拟机' },
      { id: 3, name: 'network_device', alias: '网络设备' }
    ]
  },
  {
    id: 2,
    name: '应用服务',
    ci_types: [
      { id: 4, name: 'application', alias: '应用' },
      { id: 5, name: 'service', alias: '服务' },
      { id: 6, name: 'database', alias: '数据库' }
    ]
  },
  {
    id: 3,
    name: '容器化',
    ci_types: [
      { id: 7, name: 'container', alias: '容器' },
      { id: 8, name: 'pod', alias: 'Pod' },
      { id: 9, name: 'cluster', alias: '集群' }
    ]
  }
]

// 模拟任务执行状态
export const mockTaskStatus = {
  status: 'running',
  startTime: '2024-01-16 14:30:00',
  progress: 65,
  checkedConditions: 3,
  totalConditions: 5,
  violationsFound: 2,
  processingSpeed: 3
}

// 模拟执行日志
export const mockExecutionLogs = [
  {
    timestamp: '14:30:00',
    level: 'info',
    message: '开始执行合规检查任务'
  },
  {
    timestamp: '14:30:15',
    level: 'info',
    message: '正在执行搜索条件 1: 普通搜索'
  },
  {
    timestamp: '14:30:45',
    level: 'warn',
    message: '发现 2 个违规项'
  },
  {
    timestamp: '14:31:20',
    level: 'info',
    message: '搜索条件执行完成，正在生成报告'
  }
]
