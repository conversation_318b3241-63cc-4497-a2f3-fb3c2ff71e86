import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

/**
 * 导出Excel报告
 * @param {Object} report - 报告数据
 * @param {String} filename - 文件名
 */
export function exportExcelReport(report, filename = '合规检查报告') {
  const workbook = XLSX.utils.book_new()

  // 创建摘要工作表
  const summaryData = [
    ['报告ID', report.reportId],
    ['任务名称', report.taskName],
    ['执行时间', report.executeTime],
    ['执行耗时', formatDuration(report.duration)],
    ['总违规项', report.summary.totalViolations],
    ['高风险', report.summary.severityBreakdown.high],
    ['中风险', report.summary.severityBreakdown.medium],
    ['低风险', report.summary.severityBreakdown.low]
  ]

  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData)
  XLSX.utils.book_append_sheet(workbook, summarySheet, '执行摘要')

  // 创建违规详情工作表
  const violationData = []
  violationData.push(['搜索条件', 'CI ID', 'CI类型', 'CI名称', '违规原因', '风险等级'])

  report.results.forEach((result, index) => {
    result.violations.forEach(violation => {
      violationData.push([
        `搜索条件${index + 1}`,
        violation.ciId,
        violation.ciType,
        violation.ciName,
        violation.violationReason,
        getSeverityText(violation.severity)
      ])
    })
  })

  const violationSheet = XLSX.utils.aoa_to_sheet(violationData)
  XLSX.utils.book_append_sheet(workbook, violationSheet, '违规详情')

  // 导出文件
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  saveAs(blob, `${filename}.xlsx`)
}

/**
 * 生成PDF报告（使用浏览器打印功能）
 * @param {Object} report - 报告数据
 */
export function exportPDFReport(report) {
  const printWindow = window.open('', '_blank')
  const htmlContent = generateReportHTML(report)

  printWindow.document.write(htmlContent)
  printWindow.document.close()
  printWindow.focus()

  // 延迟执行打印，确保内容加载完成
  setTimeout(() => {
    printWindow.print()
  }, 500)
}

/**
 * 生成报告HTML内容
 * @param {Object} report - 报告数据
 * @returns {String} HTML内容
 */
function generateReportHTML(report) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>合规检查报告</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { margin-bottom: 30px; }
        .summary-item { display: inline-block; margin: 10px 20px; }
        .violations { margin-top: 30px; }
        .violation-section { margin-bottom: 30px; }
        .violation-section h3 { color: #1890ff; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
        .high-risk { color: #ff4d4f; }
        .medium-risk { color: #fa8c16; }
        .low-risk { color: #1890ff; }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>数据合规检查报告</h1>
        <p>报告ID: ${report.reportId}</p>
        <p>任务名称: ${report.taskName}</p>
        <p>执行时间: ${report.executeTime}</p>
      </div>
      
      <div class="summary">
        <h2>执行摘要</h2>
        <div class="summary-item">总违规项: <strong>${report.summary.totalViolations}</strong></div>
        <div class="summary-item high-risk">高风险: <strong>${report.summary.severityBreakdown.high}</strong></div>
        <div class="summary-item medium-risk">中风险: <strong>${report.summary.severityBreakdown.medium}</strong></div>
        <div class="summary-item low-risk">低风险: <strong>${report.summary.severityBreakdown.low}</strong></div>
      </div>
      
      <div class="violations">
        <h2>违规详情</h2>
        ${report.results.map((result, index) => `
          <div class="violation-section">
            <h3>搜索条件 ${index + 1}（${getSearchTypeText(result.searchType)}）</h3>
            <p>违规项数: ${result.violationCount}</p>
            <table>
              <thead>
                <tr>
                  <th>CI ID</th>
                  <th>CI类型</th>
                  <th>CI名称</th>
                  <th>违规原因</th>
                  <th>风险等级</th>
                </tr>
              </thead>
              <tbody>
                ${result.violations.map(violation => `
                  <tr>
                    <td>${violation.ciId}</td>
                    <td>${violation.ciType}</td>
                    <td>${violation.ciName}</td>
                    <td>${violation.violationReason}</td>
                    <td class="${violation.severity}-risk">${getSeverityText(violation.severity)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        `).join('')}
      </div>
    </body>
    </html>
  `
}

/**
 * 格式化执行时间
 * @param {Number} duration - 执行耗时（毫秒）
 * @returns {String} 格式化后的时间
 */
function formatDuration(duration) {
  if (!duration) return '0秒'

  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟${seconds % 60}秒`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

/**
 * 获取风险等级文本
 * @param {String} severity - 风险等级
 * @returns {String} 风险等级文本
 */
function getSeverityText(severity) {
  const textMap = {
    'high': '高风险',
    'medium': '中风险',
    'low': '低风险'
  }
  return textMap[severity] || severity
}

/**
 * 获取搜索类型文本
 * @param {String} type - 搜索类型
 * @returns {String} 搜索类型文本
 */
function getSearchTypeText(type) {
  const typeMap = {
    'resource': '普通搜索',
    'relation': '关系搜索'
  }
  return typeMap[type] || type
}
