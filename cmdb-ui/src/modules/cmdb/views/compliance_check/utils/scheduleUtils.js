import moment from 'moment'

/**
 * 生成cron表达式
 * @param {Object} schedule - 调度配置
 * @returns {String} cron表达式
 */
export function generateCronExpression(schedule) {
  const { type, time, weekdays, monthday } = schedule

  if (type === 'once') {
    return null
  }

  const timeMoment = moment(time)
  const minute = timeMoment.minute()
  const hour = timeMoment.hour()

  switch (type) {
    case 'daily':
      return `${minute} ${hour} * * *`
    case 'weekly':
      const days = weekdays.join(',')
      return `${minute} ${hour} * * ${days}`
    case 'monthly':
      return `${minute} ${hour} ${monthday} * *`
    default:
      return null
  }
}

/**
 * 解析cron表达式为调度配置
 * @param {String} cronExpression - cron表达式
 * @returns {Object} 调度配置
 */
export function parseCronExpression(cronExpression) {
  if (!cronExpression) {
    return { type: 'once' }
  }

  const parts = cronExpression.split(' ')
  if (parts.length !== 5) {
    return { type: 'once' }
  }

  const [minute, hour, day, month, weekday] = parts

  // 每日执行
  if (day === '*' && month === '*' && weekday === '*') {
    return {
      type: 'daily',
      time: moment().hour(parseInt(hour)).minute(parseInt(minute)).format('HH:mm')
    }
  }

  // 每周执行
  if (day === '*' && month === '*' && weekday !== '*') {
    return {
      type: 'weekly',
      time: moment().hour(parseInt(hour)).minute(parseInt(minute)).format('HH:mm'),
      weekdays: weekday.split(',')
    }
  }

  // 每月执行
  if (day !== '*' && month === '*' && weekday === '*') {
    return {
      type: 'monthly',
      time: moment().hour(parseInt(hour)).minute(parseInt(minute)).format('HH:mm'),
      monthday: parseInt(day)
    }
  }

  return { type: 'once' }
}

/**
 * 格式化执行时间
 * @param {Number} duration - 执行耗时（毫秒）
 * @returns {String} 格式化后的时间
 */
export function formatDuration(duration) {
  if (!duration) return '0秒'

  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟${seconds % 60}秒`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

/**
 * 获取状态徽章类型
 * @param {String} status - 状态
 * @returns {String} 徽章类型
 */
export function getStatusBadge(status) {
  const statusMap = {
    'running': 'processing',
    'success': 'success',
    'failed': 'error',
    'enabled': 'success',
    'disabled': 'default'
  }
  return statusMap[status] || 'default'
}

/**
 * 获取状态文本
 * @param {String} status - 状态
 * @returns {String} 状态文本
 */
export function getStatusText(status) {
  const statusMap = {
    'running': '运行中',
    'success': '成功',
    'failed': '失败',
    'enabled': '已启用',
    'disabled': '已禁用'
  }
  return statusMap[status] || status
}

/**
 * 获取风险等级颜色
 * @param {String} severity - 风险等级
 * @returns {String} 颜色
 */
export function getSeverityColor(severity) {
  const colorMap = {
    'high': 'red',
    'medium': 'orange',
    'low': 'blue'
  }
  return colorMap[severity] || 'default'
}

/**
 * 获取风险等级文本
 * @param {String} severity - 风险等级
 * @returns {String} 风险等级文本
 */
export function getSeverityText(severity) {
  const textMap = {
    'high': '高风险',
    'medium': '中风险',
    'low': '低风险'
  }
  return textMap[severity] || severity
}

/**
 * 获取搜索类型文本
 * @param {String} type - 搜索类型
 * @returns {String} 搜索类型文本
 */
export function getSearchTypeText(type) {
  const typeMap = {
    'resource': '普通搜索',
    'relation': '关系搜索'
  }
  return typeMap[type] || type
}
