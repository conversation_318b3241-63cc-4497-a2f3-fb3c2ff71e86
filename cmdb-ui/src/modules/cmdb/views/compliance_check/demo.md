# 数据合规检查功能演示指南

## 功能演示步骤

### 1. 访问合规检查页面

在浏览器中访问：`/cmdb/compliance_check`

### 2. 查看任务列表

页面加载后会显示模拟的任务列表，包含3个示例任务：
- 服务器配置合规检查（已启用，状态：成功）
- 网络设备状态检查（已禁用，状态：失败）
- 应用服务关系检查（已启用，状态：运行中）

### 3. 创建新任务演示

1. 点击"创建任务"按钮
2. 在步骤1中填写：
   - 任务名称：数据库配置检查
   - 任务描述：检查数据库实例的配置参数是否符合规范

3. 在步骤2中配置搜索条件：
   - 选择"普通搜索"
   - 搜索关键词：database
   - 选择CI类型：数据库
   - 筛选条件：status:running,memory:>=16

4. 在步骤3中设置执行计划：
   - 选择"每日执行"
   - 执行时间：02:00

5. 在步骤4中配置通知：
   - 启用通知
   - 邮箱：<EMAIL>
   - 通知条件：仅发现违规时通知

6. 点击"创建"完成任务创建

### 4. 查看任务详情演示

1. 点击任务列表中的"查看"按钮
2. 在任务详情抽屉中可以看到：
   - **任务配置**：显示完整的任务配置信息
   - **执行历史**：显示历史执行记录（使用模拟数据）
   - **实时监控**：对于运行中的任务显示实时状态

### 5. 查看合规报告演示

1. 在执行历史中点击"查看报告"
2. 报告包含：
   - 执行摘要：总违规项5个（高风险2个，中风险2个，低风险1个）
   - 违规详情：具体的违规CI实例列表
   - 每个违规项包含：CI ID、CI类型、CI名称、违规原因、风险等级

### 6. 导出功能演示

在报告页面可以：
- 点击"导出PDF"：使用浏览器打印功能生成PDF
- 点击"导出Excel"：下载Excel格式的报告文件
- 点击"发送邮件"：配置收件人并发送邮件报告

### 7. 任务操作演示

在任务列表中可以：
- **编辑任务**：修改任务配置
- **立即执行**：手动触发任务执行
- **启用/禁用**：切换任务状态
- **删除任务**：删除不需要的任务

## 模拟数据说明

### 任务数据
- 3个预设任务，涵盖不同的搜索类型和配置
- 包含完整的任务配置信息（搜索条件、执行计划、通知设置）

### 执行历史
- 每个任务包含3条历史执行记录
- 显示执行时间、耗时、状态、违规项数量

### 合规报告
- 包含5个违规项的详细报告
- 涵盖不同风险等级的违规情况
- 展示完整的违规原因和CI信息

### CI类型数据
- 3个CI类型分组：基础设施、应用服务、容器化
- 每个分组包含3个CI类型
- 支持树形选择和多选功能

## 界面特性

### 响应式设计
- 支持不同屏幕尺寸
- 表格支持横向滚动
- 弹窗和抽屉自适应宽度

### 交互体验
- 步骤式表单引导
- 实时搜索预览
- 状态徽章显示
- 加载状态提示

### 数据展示
- 分页表格
- 统计图表
- 标签分类
- 颜色编码

## 技术亮点

### 组件复用
- 复用现有搜索组件逻辑
- 统一的API调用模式
- 一致的错误处理机制

### 模块化设计
- 清晰的文件组织结构
- 独立的工具函数模块
- 可复用的组件设计

### 国际化支持
- 完整的中文文本
- 支持多语言扩展
- 统一的文本管理

## 注意事项

1. **模拟数据**：当前使用模拟数据，实际部署需要对接后端API
2. **权限控制**：需要相应的CMDB权限才能访问
3. **浏览器兼容**：建议使用现代浏览器（Chrome、Firefox、Safari）
4. **网络要求**：部分功能需要网络连接（如邮件发送）

## 后续开发

### 后端集成
1. 实现合规检查API接口
2. 集成任务调度系统
3. 实现邮件通知服务
4. 添加权限验证

### 功能扩展
1. 支持更多搜索类型
2. 添加自定义规则引擎
3. 集成更多通知渠道
4. 优化报告展示效果

### 性能优化
1. 大数据量处理优化
2. 实时监控性能提升
3. 报告生成速度优化
4. 前端渲染性能优化
