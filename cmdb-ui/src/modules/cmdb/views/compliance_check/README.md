# CMDB数据合规检查功能（重构版）

## 功能概述

数据合规检查功能是基于现有搜索功能的扩展，通过创建定时任务的方式，定期执行普通搜索和关系搜索，找出不符合合规要求的CI实例，并生成报告供用户查看和邮件通知。

## 重构改进

### 1. 界面样式统一
- 主页面采用白色卡片容器，与`load_attributes`等页面保持一致的布局风格
- 使用VXE表格替代Ant Design表格，提供更好的性能和用户体验
- 集成项目统一的Pager分页组件
- 添加搜索过滤功能，支持按任务名称和状态筛选

### 2. 组件复用优化
- **普通搜索配置**：完全复用`resource_search_2/resourceSearch/components/searchInput.vue`组件
- **关系搜索配置**：完全复用`resource_search_2/relationSearch/components/searchCondition.vue`组件
- **高级筛选功能**：集成现有的FilterPopover和ConditionFilter组件，支持字段选择 + 条件设置的交互方式
- **搜索预览**：与现有搜索页面的表达式显示方式保持完全一致

### 3. 真实数据集成
- 移除对模拟数据的依赖，优先调用真实API接口
- 集成现有的`getCITypes`、`getCITypeAttributesByTypeIds`、`getCITypeRelationPath`等API
- API调用失败时自动降级到模拟数据，确保功能可用性
- 支持真实的CI类型、属性、关系路径数据

## 功能特性

### 1. 任务管理
- ✅ 任务列表展示
- ✅ 任务创建/编辑
- ✅ 任务启用/禁用
- ✅ 任务删除
- ✅ 立即执行任务

### 2. 搜索条件配置
- ✅ 普通搜索配置（复用现有ResourceSearch组件逻辑）
- ✅ 关系搜索配置（复用现有RelationSearch组件逻辑）
- ✅ 支持多个搜索条件
- ✅ 搜索条件预览

### 3. 执行计划
- ✅ 立即执行一次
- ✅ 每日执行
- ✅ 每周执行
- ✅ 每月执行
- ✅ 自定义执行时间

### 4. 通知设置
- ✅ 邮件通知开关
- ✅ 多收件人配置
- ✅ 通知条件设置（总是通知/仅违规时通知）

### 5. 执行监控
- ✅ 实时执行状态监控
- ✅ 执行日志查看
- ✅ 执行进度显示
- ✅ 执行统计信息

### 6. 报告管理
- ✅ 执行历史查看
- ✅ 违规报告展示
- ✅ 报告导出（PDF/Excel）
- ✅ 邮件发送报告

## 文件结构

```
cmdb-ui/src/modules/cmdb/views/compliance_check/
├── index.vue                          # 主页面
├── components/
│   ├── TaskForm.vue                   # 任务创建/编辑表单
│   ├── TaskDetail.vue                 # 任务详情抽屉
│   ├── TaskConfigView.vue             # 任务配置展示
│   ├── TaskMonitor.vue                # 实时监控组件
│   ├── ReportView.vue                 # 报告查看组件
│   ├── SearchConfigDisplay.vue        # 搜索配置展示组件
│   ├── ResourceSearchConfig.vue       # 普通搜索配置组件
│   └── RelationSearchConfig.vue       # 关系搜索配置组件
├── utils/
│   ├── scheduleUtils.js               # 调度工具函数
│   └── reportGenerator.js             # 报告生成工具
├── mockData.js                        # 模拟数据
└── README.md                          # 说明文档
```

## 使用说明

### 1. 创建合规检查任务

1. 点击"创建任务"按钮
2. 填写基本信息（任务名称、描述）
3. 配置搜索条件：
   - 普通搜索：设置关键词、CI类型、筛选条件
   - 关系搜索：设置源CI类型、目标CI类型、关系路径等
4. 设置执行计划（立即执行、定时执行等）
5. 配置通知设置（邮件通知）
6. 保存任务

### 2. 管理任务

- **查看任务**：点击"查看"按钮查看任务详情、执行历史、实时监控
- **编辑任务**：点击"编辑"按钮修改任务配置
- **执行任务**：点击"执行"按钮立即执行任务
- **启用/禁用**：通过"更多"菜单切换任务状态
- **删除任务**：通过"更多"菜单删除任务

### 3. 查看报告

1. 在任务详情的"执行历史"标签页中查看历史执行记录
2. 点击"查看报告"按钮查看详细的合规检查报告
3. 报告包含：
   - 执行摘要（总违规项、风险等级分布）
   - 违规详情（具体的违规CI实例）
   - 搜索条件配置信息

### 4. 导出和分享

- **导出PDF**：生成PDF格式的报告文件
- **导出Excel**：生成Excel格式的数据文件
- **发送邮件**：将报告通过邮件发送给指定收件人

## 技术实现

### 前端技术栈
- Vue 2.x + Vuex
- Ant Design Vue
- @riophae/vue-treeselect
- moment.js
- xlsx.js

### 组件复用（重构后）
- **完全复用**现有的SearchInput、FilterPopover、ConditionFilter等搜索组件
- **无缝集成**现有的CI类型选择、属性筛选、关系路径选择等功能
- **统一风格**的API调用模式和错误处理机制
- **一致的交互体验**，用户无需学习新的操作方式

### 数据处理（重构后）
- **优先使用真实API**：`getCITypes`、`getCITypeAttributesByTypeIds`、`getCITypeRelationPath`等
- **智能降级机制**：API调用失败时自动使用模拟数据，确保功能可用
- **数据格式统一**：与现有搜索功能使用相同的数据结构和格式
- **性能优化**：复用现有的数据缓存和优化机制

### 后端集成（简化）
由于复用了现有组件和API，后端集成工作大幅简化：
1. 实现合规检查特有的API接口（参考 `api/complianceCheck.js`）
2. 集成任务调度系统（如Celery）
3. 实现邮件通知功能
4. **无需重复实现**搜索相关的API，直接复用现有接口

## 国际化支持

已添加完整的中文国际化文本，支持：
- 界面文本翻译
- 错误消息翻译
- 状态文本翻译
- 操作确认文本翻译

## 注意事项

1. **权限控制**：当前配置需要 `cmdb_admin`、`CMDB_READ_ALL`、`OneOPS_Application_Admin` 或 `admin` 权限
2. **性能考虑**：大量CI实例的合规检查可能耗时较长，建议合理设置执行计划
3. **存储空间**：执行历史和报告数据会占用存储空间，建议定期清理
4. **邮件配置**：需要配置SMTP服务器才能使用邮件通知功能

## 后续扩展

可以考虑的功能扩展：
- 支持更多的通知方式（钉钉、企业微信等）
- 支持自定义违规规则
- 支持批量操作任务
- 支持任务模板功能
- 支持更丰富的报告图表展示
