import { RouteView, BasicLayout } from '@/layouts'
import { getPreference, getRelationView } from '@/modules/cmdb/api/preference'

const genCmdbRoutes = async () => {
  const routes = {
    path: '/cmdb',
    name: 'cmdb',
    component: BasicLayout,
    meta: { title: 'OMP', keepAlive: false },
    children: [
      // preference
      // views
      {
        path: '/cmdb/dashboard',
        name: 'cmdb_dashboard',
        meta: { title: 'dashboard', icon: 'ops-cmdb-dashboard', selectedIcon: 'ops-cmdb-dashboard', keepAlive: false },
        component: () => import('../views/dashboard/index_v2.vue')
      },
      {
        path: '/cmdb/topoviews',
        name: 'cmdb_topology_views',
        meta: { title: 'cmdb.menu.topologyView', appName: 'cmdb', icon: 'ops-topology_view', selectedIcon: 'ops-topology_view', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'], keepAlive: false },
        component: () => import('../views/topology_view/index.vue')
      },
      {
        path: '/cmdb/cloud2',
        name: 'cmdb_cloud',
        meta: { title: 'cmdb.menu.cloudservice', disabled: true },
      },
      {
        path: '/cmdb/cloud',
        name: 'cmdb_cloud',
        component: RouteView,
        meta: { title: 'cmdb.menu.resourcesmanage', icon: 'ops-oneterm-operation', },
        children: [
          {
            path: '/cmdb/cloud_add_resources',
            component: () => import('../views/cloud_add_resources'),
            name: 'cloud_add_resources',
            meta: { 'title': 'cmdb.menu.addresources', icon: 'ops-oneterm-operation', selectedIcon: 'ops-oneterm-operation-selected', keepAlive: false }
          },
          {
            path: '/cmdb/cloud_change_billing',
            component: () => import('../views/cloud_change_billing'),
            name: 'cloud_change_billing',
            meta: { 'title': 'cmdb.menu.changebilling', icon: 'ops-oneterm-operation', selectedIcon: 'ops-oneterm-operation-selected', keepAlive: false }
          },
          {
            path: '/cmdb/cloud_change_nonbilling',
            component: () => import('../views/cloud_change_nonbilling'),
            name: 'cloud_change_nonbilling',
            meta: { 'title': 'cmdb.menu.changenonbilling', icon: 'ops-oneterm-operation', selectedIcon: 'ops-oneterm-operation-selected', keepAlive: false }
          },
        ]
      },
      {
        path: '/cmdb/datamanage',
        name: 'cmdb_datamanage',
        component: RouteView,
        meta: { title: 'cmdb.menu.datamanage', icon: 'ops-oneterm-assetlist', },
        children: [
          {
            path: '/cmdb/cloud_batch',
            component: () => import('../views/cloud_batch'),
            name: 'cloud_batch',
            meta: { 'title': 'cmdb.menu.addrecord', icon: 'ops-oneterm-account', selectedIcon: 'ops-oneterm-account-selected', keepAlive: false }
          },
          {
            path: '/cmdb/cloud_load_batch',
            component: () => import('../views/cloud_load_batch'),
            name: 'cloud_load_batch',
            meta: { 'title': 'cmdb.menu.addload', icon: 'ops-oneterm-account', selectedIcon: 'ops-oneterm-account-selected', keepAlive: false }
          }
        ]
      },
      {
        path: '/cmdb/cloud3',
        name: 'cmdb_cloud3',
        component: RouteView,
        meta: { title: 'cmdb.menu.cloudoperationHistory', icon: 'ops-cmdb-operation', },
        children: [
          {
            path: '/cmdb/cloud_operationhistory',
            name: 'cloud_operation_history',
            hideChildrenInMenu: true,
            component: () => import('../views/cloud_operation_history/index'),
            meta: { title: 'cmdb.menu.cloudViewHistory', keepAlive: false, icon: 'ops-oneterm-login-selected', selectedIcon: 'ops-oneterm-login-selected', }
          },
          {
            path: '/cmdb/cloud_changehistory',
            name: 'cloud_changehistory',
            hideChildrenInMenu: true,
            component: () => import('../views/cloud_changehistory/index'),
            meta: { title: 'cmdb.menu.cloudchangehistory', keepAlive: false, icon: 'ops-cmdb-operation', selectedIcon: 'ops-cmdb-operation-selected', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'] }
          },
          {
            path: '/cmdb/cloud_billing_history',
            name: 'cloud_billing_history',
            hideChildrenInMenu: true,
            component: () => import('../views/cloud_billing_history/index'),
            meta: { title: 'cmdb.menu.cloudBillinghistory', keepAlive: false, icon: 'ops-cmdb-operation', selectedIcon: 'ops-cmdb-operation-selected', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'] }
          },
          {
            path: '/cmdb/cloud_load',
            component: () => import('../views/cloud_load_2'),
            name: 'cloud_load_2',
            meta: { 'title': 'cmdb.menu.loadSearch', icon: 'ops-oneterm-account', selectedIcon: 'ops-oneterm-account-selected', keepAlive: false }
          },
        ]
      },
      {
        path: '/cmdb/disabled1',
        name: 'cmdb_disabled1',
        meta: { title: 'cmdb.menu.resources', disabled: true, permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'] },
      },
      {
        path: '/cmdb/resourceviews',
        name: 'cmdb_resource_views',
        component: RouteView,
        meta: { title: 'cmdb.menu.ciTable', icon: 'ops-cmdb-resource', selectedIcon: 'ops-cmdb-resource', keepAlive: true, },
        hideChildrenInMenu: false,
        children: []
      },
      {
        path: '/cmdb/tree_views',
        component: () => import('../views/tree_views'),
        name: 'cmdb_tree_views',
        meta: { title: 'cmdb.menu.ciTree', icon: 'ops-cmdb-tree', selectedIcon: 'ops-cmdb-tree', keepAlive: false },
        hideChildrenInMenu: true,
        children: [
          {
            path: '/cmdb/tree_views/:typeId',
            name: 'cmdb_tree_views_item',
            component: () => import('../views/tree_views'),
            meta: { title: 'cmdb.menu.ciTree', keepAlive: false },
            hidden: true
          }]
      },
      // {
      //   path: '/cmdb/resourcesearch',
      //   name: 'cmdb_resource_search',
      //   meta: { title: 'cmdb.menu.ciSearch', icon: 'ops-cmdb-search', selectedIcon: 'ops-cmdb-search', keepAlive: false },
      //   component: () => import('../views/resource_search/index.vue')
      // },
      {
        path: '/cmdb/resourcesearch_v2',
        name: 'cmdb_resource_search_v2',
        meta: { title: 'cmdb.menu.ciSearchNew', icon: 'ops-cmdb-search', selectedIcon: 'ops-cmdb-search', keepAlive: false },
        component: () => import('../views/resource_search_2/index.vue')
      },
      {
        path: '/cmdb/adc',
        name: 'cmdb_auto_discovery_ci',
        meta: { title: 'cmdb.menu.adCIs', icon: 'ops-cmdb-adc', selectedIcon: 'ops-cmdb-adc', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'], keepAlive: false },
        component: () => import('../views/discoveryCI/index.vue')
      },
      {
        path: `/cmdb/cidetail/:typeId/:ciId`,
        name: 'cmdb_ci_detail',
        hidden: true,
        meta: { title: 'cmdb.menu.cidetail', keepAlive: false },
        component: () => import('../views/ci/ciDetailPage.vue')
      },
      {
        path: '/cmdb/disabled2',
        name: 'cmdb_disabled2',
        meta: { title: 'cmdb.menu.config', disabled: true, permission: ['cmdb_admin', 'CMDB_READ_ALL', 'admin'] },
      },
      {
        path: '/cmdb/preference',
        component: () => import('../views/preference/index'),
        name: 'cmdb_preference',
        meta: { title: 'cmdb.menu.preference', icon: 'ops-cmdb-preference', selectedIcon: 'ops-cmdb-preference', keepAlive: false, }
      },
      {
        path: '/cmdb/batch',
        component: () => import('../views/batch'),
        name: 'cmdb_batch',
        meta: { 'title': 'cmdb.menu.batchUpload', icon: 'ops-cmdb-batch', selectedIcon: 'ops-cmdb-batch', keepAlive: false, permission: ['cmdb_admin', 'admin'] }
      },
      {
        path: '/cmdb/ci_types',
        name: 'ci_type',
        component: () => import('../views/ci_types/index'),
        meta: { title: 'cmdb.menu.citypeManage', icon: 'ops-cmdb-citype', selectedIcon: 'ops-cmdb-citype', keepAlive: false, permission: ['cmdb_admin', 'CMDB_READ_ALL', 'admin'] }
      },
      {
        path: '/cmdb/disabled3',
        name: 'cmdb_disabled3',
        meta: { title: 'cmdb.menu.backend', disabled: true, permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'], },
      },
      {
        path: '/cmdb/citypes',
        name: 'cmdb_ci_type',
        component: RouteView,
        redirect: '/cmdb/ci_type',
        meta: { title: 'cmdb.menu.backendManage', icon: 'setting', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'], },
        children: [
          {
            path: '/cmdb/customdashboard',
            name: 'cmdb_custom_dashboard',
            component: () => import('../views/custom_dashboard/index'),
            meta: { title: 'cmdb.menu.customDashboard', keepAlive: false, icon: 'ops-cmdb-customdashboard', selectedIcon: 'ops-cmdb-customdashboard-selected', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'] }
          },
          {
            path: '/cmdb/preferencerelation',
            name: 'preference_relation',
            component: () => import('../views/preference_relation/index'),
            meta: { title: 'cmdb.menu.serviceTreeDefine', keepAlive: false, icon: 'ops-cmdb-preferencerelation', selectedIcon: 'ops-cmdb-preferencerelation-selected', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'] }
          },
          {
            path: '/cmdb/modelrelation',
            name: 'model_relation',
            hideChildrenInMenu: true,
            component: () => import('../views/model_relation/index'),
            meta: { title: 'cmdb.menu.citypeRelation', keepAlive: false, icon: 'ops-cmdb-modelrelation', selectedIcon: 'ops-cmdb-modelrelation-selected', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'] }
          },
          {
            path: '/cmdb/operationhistory',
            name: 'operation_history',
            hideChildrenInMenu: true,
            component: () => import('../views/operation_history/index'),
            meta: { title: 'cmdb.menu.operationHistory', keepAlive: false, icon: 'ops-cmdb-operation', selectedIcon: 'ops-cmdb-operation-selected', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'] }
          },
          {
            path: '/cmdb/relationtype',
            name: 'relation_type',
            hideChildrenInMenu: true,
            component: () => import('../views/relation_type/index'),
            meta: { title: 'cmdb.menu.relationType', keepAlive: false, icon: 'ops-cmdb-relationtype', selectedIcon: 'ops-cmdb-relationtype-selected', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'] }
          },
          {
            path: '/cmdb/measurement_unit',
            name: 'measurement_unit',
            hideChildrenInMenu: true,
            component: () => import('../views/measurement_unit/index'),
            meta: { title: 'cmdb.menu.measurement_unit', keepAlive: false, icon: 'ops-cmdb-relationtype', selectedIcon: 'ops-cmdb-relationtype-selected', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'] }
          },
          {
            path: '/cmdb/load_attributes',
            name: 'load_attributes',
            hideChildrenInMenu: true,
            component: () => import('../views/load_attributes/index'),
            meta: { title: 'cmdb.menu.loadAttributes', keepAlive: false, icon: 'ops-cmdb-relationtype', selectedIcon: 'ops-cmdb-relationtype-selected', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'] }
          },
          {
            path: '/cmdb/discovery',
            name: 'discovery',
            component: () => import('../views/discovery/index'),
            meta: { title: 'cmdb.menu.ad', keepAlive: false, icon: 'ops-cmdb-adr', selectedIcon: 'ops-cmdb-adr-selected', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'] }
          },
          {
            path: '/cmdb/compliance_check',
            name: 'compliance_check',
            component: () => import('../views/compliance_check/index'),
            meta: { title: 'cmdb.menu.complianceCheck', keepAlive: false, icon: 'ops-cmdb-compliance', selectedIcon: 'ops-cmdb-compliance-selected', permission: ['cmdb_admin', 'CMDB_READ_ALL', 'OneOPS_Application_Admin', 'admin'] }
          },
        ]
      }
    ]
  }
  // Dynamically add subscription items and business relationships
  const [preference, relation] = await Promise.all([getPreference(), getRelationView()])
  const resourceViewsIndex = routes.children.findIndex(item => item.name === 'cmdb_resource_views')
  preference.group_types.forEach(group => {
    if (preference.group_types.length > 1) {
      routes.children[resourceViewsIndex].children.push({
        path: `/cmdb/instances/types/group${group.id}`,
        name: `cmdb_instances_group_${group.id}`,
        meta: { title: group.name || 'other', disabled: true, style: 'margin-left: 12px' },
      })
    }
    group.ci_types.forEach(item => {
      routes.children[resourceViewsIndex].children.push({
        path: `/cmdb/instances/types/${item.id}`,
        component: () => import(`../views/ci/index`),
        name: `cmdb_${item.id}`,
        meta: { title: item.alias, keepAlive: false, typeId: item.id, name: item.name, customIcon: item.icon, },
        // hideChildrenInMenu: true // Force display of MenuItem instead of SubMenu
      })
    })
  })
  const lastTypeId = window.localStorage.getItem('ops_ci_typeid') || undefined
  if (lastTypeId && preference.type_ids.some(item => item === Number(lastTypeId))) {
    routes.redirect = `/cmdb/instances/types/${lastTypeId}`
  } else if (routes.children[resourceViewsIndex]?.children?.length > 0) {
    routes.redirect = routes.children[resourceViewsIndex].children.find(item => !item.hidden && !item.meta.disabled)?.path
  } else {
    routes.redirect = '/cmdb/dashboard'
  }
  const relationViews = relation.name2id.map(item => {
    return {
      path: `/cmdb/relationviews/${item[1]}`,
      name: `cmdb_relation_views_${item[1]}`,
      component: () => import('../views/relation_views/index'),
      meta: { title: item[0], icon: 'ops-cmdb-relation', selectedIcon: 'ops-cmdb-relation', keepAlive: false, name: item[0] },
    }
  })
  routes.children.splice(resourceViewsIndex, 0, ...relationViews)
  return routes
}

export default genCmdbRoutes
