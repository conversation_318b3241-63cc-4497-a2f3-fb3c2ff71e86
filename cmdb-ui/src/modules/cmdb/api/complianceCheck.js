import { axios } from '@/utils/request'

const urlPrefix = '/v0.1'

// 获取任务列表
export function getComplianceTasks(params) {
  return axios({
    url: urlPrefix + '/compliance/tasks',
    method: 'GET',
    params
  })
}

// 创建任务
export function createComplianceTask(data) {
  return axios({
    url: urlPrefix + '/compliance/tasks',
    method: 'POST',
    data
  })
}

// 更新任务
export function updateComplianceTask(taskId, data) {
  return axios({
    url: urlPrefix + `/compliance/tasks/${taskId}`,
    method: 'PUT',
    data
  })
}

// 删除任务
export function deleteComplianceTask(taskId) {
  return axios({
    url: urlPrefix + `/compliance/tasks/${taskId}`,
    method: 'DELETE'
  })
}

// 执行任务
export function executeComplianceTask(taskId) {
  return axios({
    url: urlPrefix + `/compliance/tasks/${taskId}/execute`,
    method: 'POST'
  })
}

// 启用/禁用任务
export function toggleComplianceTask(taskId, enabled) {
  return axios({
    url: urlPrefix + `/compliance/tasks/${taskId}/toggle`,
    method: 'PUT',
    data: { enabled }
  })
}

// 获取任务详情
export function getComplianceTask(taskId) {
  return axios({
    url: urlPrefix + `/compliance/tasks/${taskId}`,
    method: 'GET'
  })
}

// 获取执行历史
export function getTaskExecutionHistory(taskId, params) {
  return axios({
    url: urlPrefix + `/compliance/tasks/${taskId}/executions`,
    method: 'GET',
    params
  })
}

// 获取执行报告
export function getExecutionReport(executionId) {
  return axios({
    url: urlPrefix + `/compliance/executions/${executionId}/report`,
    method: 'GET'
  })
}

// 下载报告
export function downloadExecutionReport(executionId, format) {
  return axios({
    url: urlPrefix + `/compliance/executions/${executionId}/report/download`,
    method: 'GET',
    params: { format }, // 'pdf', 'excel'
    responseType: 'blob'
  })
}

// 发送邮件报告
export function sendEmailReport(executionId, emails) {
  return axios({
    url: urlPrefix + `/compliance/executions/${executionId}/email`,
    method: 'POST',
    data: { emails }
  })
}

// 获取任务执行状态
export function getTaskExecutionStatus(taskId) {
  return axios({
    url: urlPrefix + `/compliance/tasks/${taskId}/status`,
    method: 'GET'
  })
}
